/* Accordion
 * 
 * Styling AccordionContainer basically means styling the accordion pane (dijitAccordionInnerContainer)
 * and the title inside of it (dijitAccordionTitle).   There are 4 basic states to style:
 * 
 * 1. closed pane (and default styling): 
 * 		.dijitAccordionInnerContainer - container for each accordion child
 * 		.dijitAccordionTitle - title for each accordion child
 *
 * 2. active closed pane (ie, mouse down on a title bar)
 * 		.dijitAccordionInnerContainerActive - for background-color, border
 * 		.dijitAccordionInnerContainerActive dijitAccordionTitle - for text color
 * 
 * 3. open pane (expanded child)
 *		.dijitAccordionChildWrapper - wraps around the child widget (typically ContentPane)
 *			setting a margin so that there's blue trim all the way around the child
 *
 * 		These rules need to override the closed pane active:
 *
 * 		.dijitAccordionInnerContainerSelected - for background-color, border
 * 		.dijitAccordionInnerContainerSelected .dijitAccordionTitle - for text color
 * 
 * 4. hovered pane, open or closed
 * 		The selectors below affect hovering over both a closed pane (ie, hovering a title bar),
 * 		and hovering over an open pane.   Also, treat mouse down on an open pane as a hover:
 *
 * 		.dijitAccordionInnerContainerHover, .dijitAccordionInnerContainerSelectedActive - for background-color, border
 * 		.dijitAccordionInnerContainerHover .dijitAccordionTitle - for text color
 */
.claro .dijitAccordionContainer {
  border: none;
}
.claro .dijitAccordionInnerContainer {
  background-color: #efefef;
  /* gray, for closed pane */

  border: solid 1px #b5bcc7;
  margin-bottom: 1px;
  -webkit-transition-property: background-color, border;
  -moz-transition-property: background-color, border;
  transition-property: background-color, border;
  -webkit-transition-duration: 0.3s;
  -moz-transition-duration: 0.3s;
  transition-duration: 0.3s;
  -webkit-transition-timing-function: linear;
  -moz-transition-timing-function: linear;
  transition-timing-function: linear;
}
.claro .dijitAccordionTitle {
  background-color: transparent;
  /* pick up color from dijitAccordionInnerContainer */

  background-image: url("../images/standardGradient.png");
  background-repeat: repeat-x;
  background-image: -moz-linear-gradient(rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0) 100%);
  background-image: -webkit-linear-gradient(rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0) 100%);
  background-image: -o-linear-gradient(rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0) 100%);
  background-image: linear-gradient(rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0) 100%);
  _background-image: none;
  padding: 5px 7px 2px 7px;
  min-height: 17px;
  color: #494949;
}
.claro .dijitAccordionContainer .dijitAccordionChildWrapper {
  /* this extends the blue trim styling of the title bar to wrapping around the node.
	 * done by setting margin
	 */

  background-color: #ffffff;
  border: 1px solid #759dc0;
  margin: 0 2px 2px;
}
.claro .dijitAccordionContainer .dijitAccordionContainer-child {
  /* this is affecting the child widget itself */

  padding: 9px;
}
/* Hover state for closed pane */
.claro .dijitAccordionInnerContainerHover {
  border: 1px solid #759dc0;
  background-color: #abd6ff;
  -webkit-transition-duration: 0.2s;
  -moz-transition-duration: 0.2s;
  transition-duration: 0.2s;
}
.claro .dijitAccordionInnerContainerHover .dijitAccordionTitle {
  color: #000000;
}
/* Active state for closed pane */
.claro .dijitAccordionInnerContainerActive {
  border: 1px solid #759dc0;
  background-color: #7dbdfa;
  -webkit-transition-duration: 0.1s;
  -moz-transition-duration: 0.1s;
  transition-duration: 0.1s;
}
.claro .dijitAccordionInnerContainerActive .dijitAccordionTitle {
  background-image: url("../images/activeGradient.png");
  background-repeat: repeat-x;
  background-image: -moz-linear-gradient(rgba(190, 190, 190, 0.98) 0px, rgba(255, 255, 255, 0.65) 3px, rgba(255, 255, 255, 0) 100%);
  background-image: -webkit-linear-gradient(rgba(190, 190, 190, 0.98) 0px, rgba(255, 255, 255, 0.65) 3px, rgba(255, 255, 255, 0) 100%);
  background-image: -o-linear-gradient(rgba(190, 190, 190, 0.98) 0px, rgba(255, 255, 255, 0.65) 3px, rgba(255, 255, 255, 0) 100%);
  background-image: linear-gradient(rgba(190, 190, 190, 0.98) 0px, rgba(255, 255, 255, 0.65) 3px, rgba(255, 255, 255, 0) 100%);
  _background-image: none;
  color: #000000;
}
/* Open (a.k.a. selected) pane */
.claro .dijitAccordionInnerContainerSelected {
  border-color: #759dc0;
  background-color: #cfe5fa;
}
.claro .dijitAccordionInnerContainerSelected .dijitAccordionTitle {
  color: #000000;
  background-image: url("../images/standardGradient.png");
  background-repeat: repeat-x;
  background-image: -moz-linear-gradient(rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0) 100%);
  background-image: -webkit-linear-gradient(rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0) 100%);
  background-image: -o-linear-gradient(rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0) 100%);
  background-image: linear-gradient(rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0) 100%);
  _background-image: none;
  /* avoid effect when clicking the title of the open pane */

}
