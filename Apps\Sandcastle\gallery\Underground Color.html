<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="Use Viewer to start building new applications or easily embed Cesium into existing applications."
    />
    <meta name="cesium-sandcastle-labels" content="Beginner, Showcases" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
      #toolbar {
        background: rgba(42, 42, 42, 0.8);
        padding: 4px;
        border-radius: 4px;
      }
      #toolbar input {
        vertical-align: middle;
        padding-top: 2px;
        padding-bottom: 2px;
      }
      #toolbar .header {
        font-weight: bold;
      }
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar">
      <table>
        <tbody>
          <tr>
            <td>Enabled</td>
            <td>
              <input type="checkbox" data-bind="checked: enabled" />
            </td>
          </tr>
          <tr>
            <td>Near distance</td>
            <td>
              <input
                type="range"
                min="0.0"
                max="1000000.0"
                step="1.0"
                data-bind="value: nearDistance, valueUpdate: 'input'"
              />
              <input type="text" size="5" data-bind="value: nearDistance" />
            </td>
          </tr>
          <tr>
            <td>Far distance</td>
            <td>
              <input
                type="range"
                min="100000.0"
                max="3000000.0"
                step="1.0"
                data-bind="value: farDistance, valueUpdate: 'input'"
              />
              <input type="text" size="5" data-bind="value: farDistance" />
            </td>
          </tr>
          <tr>
            <td>Near alpha</td>
            <td>
              <input
                type="range"
                min="0.0"
                max="1.0"
                step="0.01"
                data-bind="value: nearAlpha, valueUpdate: 'input'"
              />
              <input type="text" size="5" data-bind="value: nearAlpha" />
            </td>
          </tr>
          <tr>
            <td>Far alpha</td>
            <td>
              <input
                type="range"
                min="0.0"
                max="1.0"
                step="0.01"
                data-bind="value: farAlpha, valueUpdate: 'input'"
              />
              <input type="text" size="5" data-bind="value: farAlpha" />
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer");

        const scene = viewer.scene;
        const globe = scene.globe;

        scene.screenSpaceCameraController.enableCollisionDetection = false;

        const longitude = -3.82518;
        const latitude = 53.11728;
        const height = -500.0;
        const position = Cesium.Cartesian3.fromDegrees(longitude, latitude, height);
        const url = "../../SampleData/models/ParcLeadMine/ParcLeadMine.glb";

        const entity = viewer.entities.add({
          name: url,
          position: position,
          model: {
            uri: url,
          },
        });

        viewer.scene.camera.setView({
          destination: new Cesium.Cartesian3(
            3827058.651471591,
            -256575.7981065622,
            5078738.238484612,
          ),
          orientation: new Cesium.HeadingPitchRoll(
            1.9765540737339418,
            -0.17352018581162754,
            0.0030147639151465455,
          ),
          endTransform: Cesium.Matrix4.IDENTITY,
        });

        const originalColor = Cesium.Color.BLACK;
        const originalNearDistance = 1000.0;
        const originalFarDistance = 1000000.0;
        const originalNearAlpha = 0.0;
        const originalFarAlpha = 1.0;

        let color = originalColor;

        const viewModel = {
          enabled: true,
          nearDistance: originalNearDistance,
          farDistance: originalFarDistance,
          nearAlpha: originalNearAlpha,
          farAlpha: originalFarAlpha,
        };

        Cesium.knockout.track(viewModel);
        const toolbar = document.getElementById("toolbar");
        Cesium.knockout.applyBindings(viewModel, toolbar);
        for (const name in viewModel) {
          if (viewModel.hasOwnProperty(name)) {
            Cesium.knockout.getObservable(viewModel, name).subscribe(update);
          }
        }

        Sandcastle.addToolbarButton("Random color", function () {
          color = Cesium.Color.fromRandom({
            alpha: 1.0,
          });
          update();
        });

        Sandcastle.addToolbarButton("Clear", function () {
          color = originalColor;
          viewModel.enabled = true;
          viewModel.nearDistance = originalNearDistance;
          viewModel.farDistance = originalFarDistance;
          viewModel.nearAlpha = originalNearAlpha;
          viewModel.farAlpha = originalFarAlpha;
          update();
        });

        function update() {
          globe.undergroundColor = viewModel.enabled ? color : undefined;

          let nearDistance = Number(viewModel.nearDistance);
          nearDistance = isNaN(nearDistance) ? originalNearDistance : nearDistance;

          let farDistance = Number(viewModel.farDistance);
          farDistance = isNaN(farDistance) ? originalFarDistance : farDistance;

          if (nearDistance > farDistance) {
            nearDistance = farDistance;
          }

          let nearAlpha = Number(viewModel.nearAlpha);
          nearAlpha = isNaN(nearAlpha) ? 0.0 : nearAlpha;

          let farAlpha = Number(viewModel.farAlpha);
          farAlpha = isNaN(farAlpha) ? 1.0 : farAlpha;

          globe.undergroundColorAlphaByDistance.near = nearDistance;
          globe.undergroundColorAlphaByDistance.far = farDistance;
          globe.undergroundColorAlphaByDistance.nearValue = nearAlpha;
          globe.undergroundColorAlphaByDistance.farValue = farAlpha;
        }

        color = Cesium.Color.LIGHTSLATEGRAY;
        update(); //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
