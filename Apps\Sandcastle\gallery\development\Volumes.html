<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="Draw 3D raycast ellipsoids, using different radii on different axes."
    />
    <meta name="cesium-sandcastle-labels" content="Development" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        function createPrimitives(scene) {
          const e = scene.primitives.add(
            new Cesium.EllipsoidPrimitive({
              center: Cesium.Cartesian3.fromDegrees(-75.0, 40.0, 500000.0),
              radii: new Cesium.Cartesian3(500000.0, 500000.0, 500000.0),
              material: Cesium.Material.fromType(Cesium.Material.RimLightingType),
            }),
          );
          Sandcastle.declare(e); // For Sandcastle highlighting.

          const e2 = scene.primitives.add(
            new Cesium.EllipsoidPrimitive({
              modelMatrix: Cesium.Transforms.eastNorthUpToFixedFrame(
                Cesium.Cartesian3.fromDegrees(-95.0, 40.0, 500000.0),
              ),
              radii: new Cesium.Cartesian3(300000.0, 300000.0, 500000.0),
              material: Cesium.Material.fromType(Cesium.Material.StripeType),
            }),
          );
          Sandcastle.declare(e2); // For Sandcastle highlighting.
        }

        const viewer = new Cesium.Viewer("cesiumContainer");
        viewer.screenSpaceEventHandler.setInputAction(function (movement) {
          const pickedPrimitive = viewer.scene.pick(movement.endPosition);
          Sandcastle.highlight(pickedPrimitive);
        }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

        createPrimitives(viewer.scene);
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
