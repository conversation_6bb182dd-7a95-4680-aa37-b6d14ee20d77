<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="Draw polylines with different material appearances."
    />
    <meta name="cesium-sandcastle-labels" content="Development" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer");
        const scene = viewer.scene;

        // Polyline Glow
        scene.primitives.add(
          new Cesium.Primitive({
            geometryInstances: new Cesium.GeometryInstance({
              geometry: new Cesium.PolylineGeometry({
                positions: Cesium.Cartesian3.fromDegreesArray([
                  -120.0, 40.0, -80.0, 40.0,
                ]),
                width: 10.0,
                vertexFormat: Cesium.PolylineMaterialAppearance.VERTEX_FORMAT,
              }),
            }),
            appearance: new Cesium.PolylineMaterialAppearance({
              material: Cesium.Material.fromType(Cesium.Material.PolylineGlowType),
            }),
          }),
        );

        // Polyline Arrow
        scene.primitives.add(
          new Cesium.Primitive({
            geometryInstances: new Cesium.GeometryInstance({
              geometry: new Cesium.PolylineGeometry({
                positions: Cesium.Cartesian3.fromDegreesArray([
                  -120.0, 35.0, -80.0, 35.0,
                ]),
                width: 10.0,
                vertexFormat: Cesium.PolylineMaterialAppearance.VERTEX_FORMAT,
              }),
            }),
            appearance: new Cesium.PolylineMaterialAppearance({
              material: Cesium.Material.fromType(Cesium.Material.PolylineArrowType),
            }),
          }),
        );

        // Polyline Outline
        scene.primitives.add(
          new Cesium.Primitive({
            geometryInstances: new Cesium.GeometryInstance({
              geometry: new Cesium.PolylineGeometry({
                positions: Cesium.Cartesian3.fromDegreesArray([
                  -120.0, 30.0, -80.0, 30.0,
                ]),
                width: 10.0,
                vertexFormat: Cesium.PolylineMaterialAppearance.VERTEX_FORMAT,
              }),
            }),
            appearance: new Cesium.PolylineMaterialAppearance({
              material: Cesium.Material.fromType(Cesium.Material.PolylineOutlineType),
            }),
          }),
        );
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
