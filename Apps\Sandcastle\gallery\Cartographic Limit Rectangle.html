<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="Limit terrain and imagery to a cartographic Rectangle area."
    />
    <meta name="cesium-sandcastle-labels" content="Showcases" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer", {
          terrain: Cesium.Terrain.fromWorldTerrain(),
        });

        const scene = viewer.scene;
        const globe = scene.globe;

        // Tropics of Cancer and Capricorn
        const coffeeBeltRectangle = Cesium.Rectangle.fromDegrees(
          -180.0,
          -23.43687,
          180.0,
          23.43687,
        );

        globe.cartographicLimitRectangle = coffeeBeltRectangle;
        globe.showSkirts = false;
        globe.backFaceCulling = false;
        globe.undergroundColor = undefined;
        scene.skyAtmosphere.show = false;

        // Add rectangles to show bounds
        const rectangles = [];

        for (let i = 0; i < 10; i++) {
          rectangles.push(
            viewer.entities.add({
              rectangle: {
                coordinates: coffeeBeltRectangle,
                material: Cesium.Color.WHITE.withAlpha(0.0),
                height: i * 5000.0,
                outline: true,
                outlineWidth: 4.0,
                outlineColor: Cesium.Color.WHITE,
              },
            }),
          );
        }

        Sandcastle.addToggleButton("Limit Enabled", true, function (checked) {
          if (checked) {
            viewer.scene.globe.cartographicLimitRectangle = coffeeBeltRectangle;
          } else {
            viewer.scene.globe.cartographicLimitRectangle = undefined;
          }
        });

        Sandcastle.addToggleButton("Show Bounds", true, function (checked) {
          const rectanglesLength = rectangles.length;
          for (let i = 0; i < rectanglesLength; i++) {
            const rectangleEntity = rectangles[i];
            rectangleEntity.show = checked;
          }
        }); //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
