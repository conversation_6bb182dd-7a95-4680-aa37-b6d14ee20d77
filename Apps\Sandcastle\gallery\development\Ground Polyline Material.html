<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="Draw Ground Polylines with different material appearances."
    />
    <meta name="cesium-sandcastle-labels" content="Development" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer", {
          terrain: Cesium.Terrain.fromWorldTerrain(),
        });
        const scene = viewer.scene;

        if (!Cesium.GroundPolylinePrimitive.isSupported(scene)) {
          window.alert("Polylines on terrain are not supported on this platform.");
        }

        // Polyline Glow
        scene.groundPrimitives.add(
          new Cesium.GroundPolylinePrimitive({
            geometryInstances: new Cesium.GeometryInstance({
              geometry: new Cesium.GroundPolylineGeometry({
                positions: Cesium.Cartesian3.fromDegreesArray([
                  -122.2558, 46.1955, -122.1058, 46.1955,
                ]),
                width: 10.0,
              }),
            }),
            appearance: new Cesium.PolylineMaterialAppearance({
              material: Cesium.Material.fromType(Cesium.Material.PolylineGlowType),
            }),
          }),
        );

        // Polyline Dash
        scene.groundPrimitives.add(
          new Cesium.GroundPolylinePrimitive({
            geometryInstances: new Cesium.GeometryInstance({
              geometry: new Cesium.GroundPolylineGeometry({
                positions: Cesium.Cartesian3.fromDegreesArray([
                  -122.2558, 46.1975, -122.1058, 46.1975,
                ]),
                width: 10.0,
              }),
            }),
            appearance: new Cesium.PolylineMaterialAppearance({
              material: Cesium.Material.fromType(Cesium.Material.PolylineDashType),
            }),
          }),
        );

        // Polyline Outline
        scene.groundPrimitives.add(
          new Cesium.GroundPolylinePrimitive({
            geometryInstances: new Cesium.GeometryInstance({
              geometry: new Cesium.GroundPolylineGeometry({
                positions: Cesium.Cartesian3.fromDegreesArray([
                  -122.2558, 46.1995, -122.1058, 46.1995,
                ]),
                width: 10.0,
              }),
            }),
            appearance: new Cesium.PolylineMaterialAppearance({
              material: Cesium.Material.fromType(Cesium.Material.PolylineOutlineType),
            }),
          }),
        );

        viewer.camera.lookAt(
          Cesium.Cartesian3.fromDegrees(-122.2058, 46.1955, 1000.0),
          new Cesium.Cartesian3(5000.0, 5000.0, 5000.0),
        );
        viewer.camera.lookAtTransform(Cesium.Matrix4.IDENTITY); //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
