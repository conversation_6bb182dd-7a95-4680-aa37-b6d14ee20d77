{"asset": {"version": "2.0"}, "extensionsUsed": ["EXT_instance_features", "EXT_structural_metadata", "EXT_mesh_gpu_instancing"], "extensionsRequired": ["EXT_mesh_gpu_instancing"], "extensions": {"EXT_structural_metadata": {"schema": {"classes": {"box": {"properties": {"name": {"type": "STRING"}, "volume": {"type": "SCALAR", "componentType": "FLOAT32"}}}, "section": {"properties": {"name": {"type": "STRING"}, "id": {"type": "SCALAR", "componentType": "UINT16"}}}}}, "propertyTables": [{"name": "Box", "class": "box", "count": 4, "properties": {"name": {"stringOffsetType": "UINT16", "values": 7, "stringOffsets": 8}, "volume": {"values": 9}}}, {"name": "Section", "class": "section", "count": 2, "properties": {"name": {"stringOffsetType": "UINT16", "values": 10, "stringOffsets": 11}, "id": {"values": 12}}}]}}, "scene": 0, "scenes": [{"nodes": [0]}], "nodes": [{"mesh": 0, "extensions": {"EXT_mesh_gpu_instancing": {"attributes": {"TRANSLATION": 3, "ROTATION": 4, "SCALE": 5, "_FEATURE_ID_0": 6}}, "EXT_instance_features": {"featureIds": [{"label": "perInstance", "propertyTable": 0, "featureCount": 4}, {"label": "section", "propertyTable": 1, "attribute": 0, "featureCount": 2}]}}}], "meshes": [{"primitives": [{"attributes": {"POSITION": 0, "NORMAL": 1}, "indices": 2, "mode": 4}]}], "accessors": [{"name": "Positions", "bufferView": 0, "byteOffset": 0, "componentType": 5126, "count": 24, "max": [0.5, 0.5, 0.5], "min": [-0.5, -0.5, -0.5], "type": "VEC3"}, {"name": "Normals", "bufferView": 1, "byteOffset": 0, "componentType": 5126, "count": 24, "type": "VEC3"}, {"name": "Indices", "bufferView": 2, "byteOffset": 0, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"name": "Instance Translations", "bufferView": 3, "byteOffset": 0, "componentType": 5126, "count": 4, "type": "VEC3"}, {"name": "Instance Rotations", "bufferView": 4, "byteOffset": 0, "componentType": 5126, "count": 4, "type": "VEC4"}, {"name": "Instance Scales", "bufferView": 5, "byteOffset": 0, "componentType": 5126, "count": 4, "type": "VEC3"}, {"name": "Instance Feature IDs", "bufferView": 6, "byteOffset": 0, "componentType": 5126, "count": 4, "type": "SCALAR"}], "bufferViews": [{"name": "Positions", "buffer": 0, "byteLength": 288, "byteOffset": 0}, {"name": "Normals", "buffer": 0, "byteLength": 288, "byteOffset": 288}, {"name": "Indices", "buffer": 0, "byteLength": 72, "byteOffset": 576}, {"name": "Instance Translations", "buffer": 1, "byteLength": 48, "byteOffset": 0}, {"name": "Instance Rotations", "buffer": 1, "byteLength": 64, "byteOffset": 48}, {"name": "Instance Scales", "buffer": 1, "byteLength": 48, "byteOffset": 112}, {"name": "Instance Feature IDs", "buffer": 1, "byteLength": 16, "byteOffset": 160}, {"name": "Box Names", "buffer": 2, "byteLength": 40, "byteOffset": 0}, {"name": "Box Name Offsets", "buffer": 2, "byteLength": 10, "byteOffset": 40}, {"name": "Box Volumes", "buffer": 2, "byteLength": 16, "byteOffset": 56}, {"name": "Section Name", "buffer": 2, "byteLength": 9, "byteOffset": 72}, {"name": "Section Name Offsets", "buffer": 2, "byteLength": 6, "byteOffset": 88}, {"name": "Section IDs", "buffer": 2, "byteLength": 4, "byteOffset": 96}], "buffers": [{"name": "Geometry Buffer", "byteLength": 648, "uri": "geometry.bin"}, {"name": "Instances Buffer", "byteLength": 176, "uri": "instances.bin"}, {"name": "<PERSON><PERSON><PERSON>", "byteLength": 104, "uri": "metadata.bin"}]}