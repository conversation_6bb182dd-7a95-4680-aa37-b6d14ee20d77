/* CheckBox
 * 
 * Styling CheckBox mainly includes:
 * 
 * 1. Containers
 * 		.dijitCheckBox|.dijitCheckBoxIcon - for border, padding, width|height and background image
 * 
 * 2. CheckBox within ToggleButton
 * 		.dijitToggleButton|.dijitToggleButtonChecked .* - for background image
 * 
 * 3. Checked state
 * 		.dijitCheckBoxChecked - for checked background-color|image
 * 		.dijitToggleButtonChecked - for border, background-color|image, display and width|height
 * 
 * 4. Hover state
 * 		.dijitCheckBoxHover|.dijitCheckBoxCheckedHover - for background image
 * 
 * 5. Disabled state
 * 		.dijitCheckBoxDisabled|.dijitCheckBoxCheckedDisabled - for background image
 */

@import "../variables";

.claro .dijitToggleButton .dijitCheckBoxIcon {
	background-image: url("../@{image-checkmark}");
}

.dj_ie6 .claro .dijitToggleButton .dijitCheckBoxIcon {
	background-image: url("../@{image-checkmark-ie6}");
}

.claro .dijitCheckBox,
.claro .dijitCheckBoxIcon		/* inside a toggle button */	{
	background-image: url("../@{image-form-checkbox-and-radios}"); /* checkbox sprite image */
	background-repeat: no-repeat;
	width: 15px;
	height: 16px;
	margin: 0 2px 0 0;
	padding: 0;
}

.dj_ie6 .claro .dijitCheckBox,
.dj_ie6 .claro .dijitCheckBoxIcon		/* inside a toggle button */	{
	background-image: url("../@{image-form-checkbox-and-radios-ie6}"); /* checkbox sprite image */
}

.claro .dijitCheckBox,
.claro .dijitToggleButton .dijitCheckBoxIcon {
	/* unchecked */
	background-position: -15px;
}

.claro .dijitCheckBoxChecked,
.claro .dijitToggleButtonChecked .dijitCheckBoxIcon {
	/* checked */
	background-position: -0;
}

.claro .dijitCheckBoxDisabled {
	/* disabled */
	background-position: -75px;
}

.claro .dijitCheckBoxCheckedDisabled {
	/* disabled but checked */
	background-position: -60px;
}

.claro .dijitCheckBoxHover {
	/* hovering over an unchecked enabled checkbox */
	background-position: -45px;
}

.claro .dijitCheckBoxCheckedHover {
	/* hovering over an checked enabled checkbox */
	background-position: -30px;
}


