eclipse.preferences.version=1
org.eclipse.wst.jsdt.core.compiler.problem.deprecation=warning
org.eclipse.wst.jsdt.core.compiler.problem.deprecationInDeprecatedCode=disabled
org.eclipse.wst.jsdt.core.compiler.problem.deprecationWhenOverridingDeprecatedMethod=disabled
org.eclipse.wst.jsdt.core.compiler.problem.discouragedReference=warning
org.eclipse.wst.jsdt.core.compiler.problem.duplicateLocalVariables=warning
org.eclipse.wst.jsdt.core.compiler.problem.emptyStatement=warning
org.eclipse.wst.jsdt.core.compiler.problem.fallthroughCase=warning
org.eclipse.wst.jsdt.core.compiler.problem.fieldHiding=ignore
org.eclipse.wst.jsdt.core.compiler.problem.finallyBlockNotCompletingNormally=warning
org.eclipse.wst.jsdt.core.compiler.problem.forbiddenReference=error
org.eclipse.wst.jsdt.core.compiler.problem.hiddenCatchBlock=warning
org.eclipse.wst.jsdt.core.compiler.problem.indirectStaticAccess=ignore
org.eclipse.wst.jsdt.core.compiler.problem.localVariableHiding=ignore
org.eclipse.wst.jsdt.core.compiler.problem.looseVarDecleration=warning
org.eclipse.wst.jsdt.core.compiler.problem.noEffectAssignment=warning
org.eclipse.wst.jsdt.core.compiler.problem.nonExternalizedStringLiteral=ignore
org.eclipse.wst.jsdt.core.compiler.problem.nullReference=ignore
org.eclipse.wst.jsdt.core.compiler.problem.optionalSemicolon=warning
org.eclipse.wst.jsdt.core.compiler.problem.parameterAssignment=ignore
org.eclipse.wst.jsdt.core.compiler.problem.possibleAccidentalBooleanAssignment=ignore
org.eclipse.wst.jsdt.core.compiler.problem.potentialNullReference=ignore
org.eclipse.wst.jsdt.core.compiler.problem.redundantNullCheck=ignore
org.eclipse.wst.jsdt.core.compiler.problem.undefinedField=warning
org.eclipse.wst.jsdt.core.compiler.problem.undocumentedEmptyBlock=ignore
org.eclipse.wst.jsdt.core.compiler.problem.uninitializedGlobalVariable=ignore
org.eclipse.wst.jsdt.core.compiler.problem.uninitializedLocalVariable=ignore
org.eclipse.wst.jsdt.core.compiler.problem.unnecessaryElse=warning
org.eclipse.wst.jsdt.core.compiler.problem.unresolvedFieldReference=error
org.eclipse.wst.jsdt.core.compiler.problem.unresolvedMethodReference=error
org.eclipse.wst.jsdt.core.compiler.problem.unresolvedTypeReference=error
org.eclipse.wst.jsdt.core.compiler.problem.unusedDeclaredThrownExceptionWhenOverriding=disabled
org.eclipse.wst.jsdt.core.compiler.problem.unusedLabel=warning
org.eclipse.wst.jsdt.core.compiler.problem.unusedLocal=ignore
org.eclipse.wst.jsdt.core.compiler.problem.unusedParameter=ignore
org.eclipse.wst.jsdt.core.compiler.problem.unusedParameterIncludeDocCommentReference=enabled
org.eclipse.wst.jsdt.core.compiler.problem.unusedParameterWhenImplementingAbstract=disabled
org.eclipse.wst.jsdt.core.compiler.problem.unusedPrivateMember=warning
org.eclipse.wst.jsdt.core.formatter.align_type_members_on_columns=false
org.eclipse.wst.jsdt.core.formatter.alignment_for_arguments_in_allocation_expression=16
org.eclipse.wst.jsdt.core.formatter.alignment_for_arguments_in_enum_constant=16
org.eclipse.wst.jsdt.core.formatter.alignment_for_arguments_in_explicit_constructor_call=16
org.eclipse.wst.jsdt.core.formatter.alignment_for_arguments_in_method_invocation=16
org.eclipse.wst.jsdt.core.formatter.alignment_for_arguments_in_qualified_allocation_expression=16
org.eclipse.wst.jsdt.core.formatter.alignment_for_assignment=0
org.eclipse.wst.jsdt.core.formatter.alignment_for_binary_expression=16
org.eclipse.wst.jsdt.core.formatter.alignment_for_compact_if=16
org.eclipse.wst.jsdt.core.formatter.alignment_for_conditional_expression=80
org.eclipse.wst.jsdt.core.formatter.alignment_for_enum_constants=0
org.eclipse.wst.jsdt.core.formatter.alignment_for_expressions_in_array_initializer=16
org.eclipse.wst.jsdt.core.formatter.alignment_for_multiple_fields=16
org.eclipse.wst.jsdt.core.formatter.alignment_for_parameters_in_constructor_declaration=16
org.eclipse.wst.jsdt.core.formatter.alignment_for_parameters_in_method_declaration=16
org.eclipse.wst.jsdt.core.formatter.alignment_for_selector_in_method_invocation=16
org.eclipse.wst.jsdt.core.formatter.alignment_for_superclass_in_type_declaration=16
org.eclipse.wst.jsdt.core.formatter.alignment_for_superinterfaces_in_enum_declaration=16
org.eclipse.wst.jsdt.core.formatter.alignment_for_superinterfaces_in_type_declaration=16
org.eclipse.wst.jsdt.core.formatter.alignment_for_throws_clause_in_constructor_declaration=16
org.eclipse.wst.jsdt.core.formatter.alignment_for_throws_clause_in_method_declaration=16
org.eclipse.wst.jsdt.core.formatter.blank_lines_after_imports=1
org.eclipse.wst.jsdt.core.formatter.blank_lines_after_package=1
org.eclipse.wst.jsdt.core.formatter.blank_lines_before_field=0
org.eclipse.wst.jsdt.core.formatter.blank_lines_before_first_class_body_declaration=0
org.eclipse.wst.jsdt.core.formatter.blank_lines_before_imports=1
org.eclipse.wst.jsdt.core.formatter.blank_lines_before_member_type=1
org.eclipse.wst.jsdt.core.formatter.blank_lines_before_method=1
org.eclipse.wst.jsdt.core.formatter.blank_lines_before_new_chunk=1
org.eclipse.wst.jsdt.core.formatter.blank_lines_before_package=0
org.eclipse.wst.jsdt.core.formatter.blank_lines_between_import_groups=1
org.eclipse.wst.jsdt.core.formatter.blank_lines_between_type_declarations=0
org.eclipse.wst.jsdt.core.formatter.brace_position_for_annotation_type_declaration=end_of_line
org.eclipse.wst.jsdt.core.formatter.brace_position_for_anonymous_type_declaration=end_of_line
org.eclipse.wst.jsdt.core.formatter.brace_position_for_array_initializer=end_of_line
org.eclipse.wst.jsdt.core.formatter.brace_position_for_block=end_of_line
org.eclipse.wst.jsdt.core.formatter.brace_position_for_block_in_case=end_of_line
org.eclipse.wst.jsdt.core.formatter.brace_position_for_constructor_declaration=end_of_line
org.eclipse.wst.jsdt.core.formatter.brace_position_for_enum_constant=end_of_line
org.eclipse.wst.jsdt.core.formatter.brace_position_for_enum_declaration=end_of_line
org.eclipse.wst.jsdt.core.formatter.brace_position_for_method_declaration=end_of_line
org.eclipse.wst.jsdt.core.formatter.brace_position_for_objlit_initializer=end_of_line
org.eclipse.wst.jsdt.core.formatter.brace_position_for_switch=end_of_line
org.eclipse.wst.jsdt.core.formatter.brace_position_for_type_declaration=end_of_line
org.eclipse.wst.jsdt.core.formatter.comment.clear_blank_lines_in_block_comment=false
org.eclipse.wst.jsdt.core.formatter.comment.clear_blank_lines_in_javadoc_comment=false
org.eclipse.wst.jsdt.core.formatter.comment.format_block_comments=false
org.eclipse.wst.jsdt.core.formatter.comment.format_header=false
org.eclipse.wst.jsdt.core.formatter.comment.format_html=true
org.eclipse.wst.jsdt.core.formatter.comment.format_javadoc_comments=false
org.eclipse.wst.jsdt.core.formatter.comment.format_line_comments=false
org.eclipse.wst.jsdt.core.formatter.comment.format_source_code=true
org.eclipse.wst.jsdt.core.formatter.comment.indent_parameter_description=true
org.eclipse.wst.jsdt.core.formatter.comment.indent_root_tags=true
org.eclipse.wst.jsdt.core.formatter.comment.insert_new_line_before_root_tags=insert
org.eclipse.wst.jsdt.core.formatter.comment.insert_new_line_for_parameter=insert
org.eclipse.wst.jsdt.core.formatter.comment.line_length=80
org.eclipse.wst.jsdt.core.formatter.compact_else_if=true
org.eclipse.wst.jsdt.core.formatter.continuation_indentation=2
org.eclipse.wst.jsdt.core.formatter.continuation_indentation_for_array_initializer=2
org.eclipse.wst.jsdt.core.formatter.continuation_indentation_for_objlit_initializer=1
org.eclipse.wst.jsdt.core.formatter.format_guardian_clause_on_one_line=false
org.eclipse.wst.jsdt.core.formatter.indent_body_declarations_compare_to_annotation_declaration_header=true
org.eclipse.wst.jsdt.core.formatter.indent_body_declarations_compare_to_enum_constant_header=true
org.eclipse.wst.jsdt.core.formatter.indent_body_declarations_compare_to_enum_declaration_header=true
org.eclipse.wst.jsdt.core.formatter.indent_body_declarations_compare_to_type_header=true
org.eclipse.wst.jsdt.core.formatter.indent_breaks_compare_to_cases=true
org.eclipse.wst.jsdt.core.formatter.indent_empty_lines=false
org.eclipse.wst.jsdt.core.formatter.indent_statements_compare_to_block=true
org.eclipse.wst.jsdt.core.formatter.indent_statements_compare_to_body=true
org.eclipse.wst.jsdt.core.formatter.indent_switchstatements_compare_to_cases=true
org.eclipse.wst.jsdt.core.formatter.indent_switchstatements_compare_to_switch=false
org.eclipse.wst.jsdt.core.formatter.indentation.size=4
org.eclipse.wst.jsdt.core.formatter.insert_new_line_after_annotation=insert
org.eclipse.wst.jsdt.core.formatter.insert_new_line_after_comma_in_objlit_initializer=insert
org.eclipse.wst.jsdt.core.formatter.insert_new_line_after_opening_brace_in_array_initializer=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_new_line_after_opening_brace_in_objlit_initializer=insert
org.eclipse.wst.jsdt.core.formatter.insert_new_line_at_end_of_file_if_missing=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_new_line_before_catch_in_try_statement=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_new_line_before_closing_brace_in_array_initializer=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_new_line_before_closing_brace_in_objlit_initializer=insert
org.eclipse.wst.jsdt.core.formatter.insert_new_line_before_else_in_if_statement=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_new_line_before_finally_in_try_statement=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_new_line_before_while_in_do_statement=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_new_line_in_empty_annotation_declaration=insert
org.eclipse.wst.jsdt.core.formatter.insert_new_line_in_empty_anonymous_type_declaration=insert
org.eclipse.wst.jsdt.core.formatter.insert_new_line_in_empty_block=insert
org.eclipse.wst.jsdt.core.formatter.insert_new_line_in_empty_enum_constant=insert
org.eclipse.wst.jsdt.core.formatter.insert_new_line_in_empty_enum_declaration=insert
org.eclipse.wst.jsdt.core.formatter.insert_new_line_in_empty_method_body=insert
org.eclipse.wst.jsdt.core.formatter.insert_new_line_in_empty_type_declaration=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_and_in_type_parameter=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_assignment_operator=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_at_in_annotation=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_at_in_annotation_type_declaration=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_binary_operator=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_closing_angle_bracket_in_type_arguments=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_closing_angle_bracket_in_type_parameters=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_closing_brace_in_block=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_closing_paren_in_cast=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_colon_in_assert=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_colon_in_case=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_colon_in_conditional=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_colon_in_for=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_colon_in_labeled_statement=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_colon_in_object_initializer=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_comma_in_allocation_expression=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_comma_in_annotation=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_comma_in_array_initializer=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_comma_in_constructor_declaration_parameters=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_comma_in_constructor_declaration_throws=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_comma_in_enum_constant_arguments=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_comma_in_enum_declarations=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_comma_in_explicitconstructorcall_arguments=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_comma_in_for_increments=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_comma_in_for_inits=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_comma_in_method_declaration_parameters=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_comma_in_method_declaration_throws=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_comma_in_method_invocation_arguments=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_comma_in_multiple_field_declarations=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_comma_in_multiple_local_declarations=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_comma_in_parameterized_type_reference=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_comma_in_superinterfaces=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_comma_in_type_arguments=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_comma_in_type_parameters=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_ellipsis=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_function_keyword=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_opening_angle_bracket_in_parameterized_type_reference=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_opening_angle_bracket_in_type_arguments=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_opening_angle_bracket_in_type_parameters=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_opening_brace_in_array_initializer=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_opening_bracket_in_array_allocation_expression=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_opening_bracket_in_array_reference=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_opening_paren_in_annotation=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_opening_paren_in_cast=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_opening_paren_in_catch=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_opening_paren_in_constructor_declaration=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_opening_paren_in_enum_constant=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_opening_paren_in_for=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_opening_paren_in_if=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_opening_paren_in_method_declaration=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_opening_paren_in_method_invocation=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_opening_paren_in_parenthesized_expression=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_opening_paren_in_switch=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_opening_paren_in_synchronized=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_opening_paren_in_while=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_postfix_operator=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_prefix_operator=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_question_in_conditional=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_semicolon_in_for=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_after_unary_operator=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_assignment_operator=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_at_in_annotation_type_declaration=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_binary_operator=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_closing_angle_bracket_in_parameterized_type_reference=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_closing_angle_bracket_in_type_arguments=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_closing_angle_bracket_in_type_parameters=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_closing_brace_in_array_initializer=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_closing_bracket_in_array_allocation_expression=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_closing_bracket_in_array_reference=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_closing_paren_in_annotation=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_closing_paren_in_cast=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_closing_paren_in_catch=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_closing_paren_in_constructor_declaration=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_closing_paren_in_enum_constant=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_closing_paren_in_for=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_closing_paren_in_if=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_closing_paren_in_method_declaration=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_closing_paren_in_method_invocation=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_closing_paren_in_parenthesized_expression=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_closing_paren_in_switch=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_closing_paren_in_synchronized=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_closing_paren_in_while=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_colon_in_assert=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_colon_in_case=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_colon_in_conditional=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_colon_in_default=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_colon_in_for=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_colon_in_labeled_statement=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_colon_in_object_initializer=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_comma_in_allocation_expression=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_comma_in_annotation=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_comma_in_array_initializer=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_comma_in_constructor_declaration_parameters=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_comma_in_constructor_declaration_throws=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_comma_in_enum_constant_arguments=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_comma_in_enum_declarations=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_comma_in_explicitconstructorcall_arguments=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_comma_in_for_increments=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_comma_in_for_inits=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_comma_in_method_declaration_parameters=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_comma_in_method_declaration_throws=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_comma_in_method_invocation_arguments=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_comma_in_multiple_field_declarations=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_comma_in_multiple_local_declarations=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_comma_in_parameterized_type_reference=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_comma_in_superinterfaces=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_comma_in_type_arguments=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_comma_in_type_parameters=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_ellipsis=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_opening_angle_bracket_in_parameterized_type_reference=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_opening_angle_bracket_in_type_arguments=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_opening_angle_bracket_in_type_parameters=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_opening_brace_in_annotation_type_declaration=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_opening_brace_in_anonymous_type_declaration=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_opening_brace_in_block=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_opening_brace_in_constructor_declaration=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_opening_brace_in_enum_constant=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_opening_brace_in_enum_declaration=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_opening_brace_in_method_declaration=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_opening_brace_in_switch=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_opening_brace_in_type_declaration=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_opening_bracket_in_array_allocation_expression=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_opening_bracket_in_array_reference=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_opening_bracket_in_array_type_reference=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_opening_paren_in_annotation=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_opening_paren_in_annotation_type_member_declaration=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_opening_paren_in_catch=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_opening_paren_in_constructor_declaration=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_opening_paren_in_enum_constant=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_opening_paren_in_for=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_opening_paren_in_if=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_opening_paren_in_method_declaration=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_opening_paren_in_method_invocation=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_opening_paren_in_parenthesized_expression=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_opening_paren_in_switch=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_opening_paren_in_synchronized=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_opening_paren_in_while=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_parenthesized_expression_in_return=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_parenthesized_expression_in_throw=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_postfix_operator=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_prefix_operator=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_question_in_conditional=insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_semicolon=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_semicolon_in_for=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_before_unary_operator=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_between_brackets_in_array_type_reference=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_between_empty_braces_in_array_initializer=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_between_empty_brackets_in_array_allocation_expression=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_between_empty_parens_in_annotation_type_member_declaration=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_between_empty_parens_in_constructor_declaration=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_between_empty_parens_in_enum_constant=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_between_empty_parens_in_method_declaration=do not insert
org.eclipse.wst.jsdt.core.formatter.insert_space_between_empty_parens_in_method_invocation=do not insert
org.eclipse.wst.jsdt.core.formatter.keep_else_statement_on_same_line=false
org.eclipse.wst.jsdt.core.formatter.keep_empty_array_initializer_on_one_line=false
org.eclipse.wst.jsdt.core.formatter.keep_empty_objlit_initializer_on_one_line=false
org.eclipse.wst.jsdt.core.formatter.keep_imple_if_on_one_line=false
org.eclipse.wst.jsdt.core.formatter.keep_then_statement_on_same_line=false
org.eclipse.wst.jsdt.core.formatter.lineSplit=9999
org.eclipse.wst.jsdt.core.formatter.never_indent_block_comments_on_first_column=false
org.eclipse.wst.jsdt.core.formatter.never_indent_line_comments_on_first_column=false
org.eclipse.wst.jsdt.core.formatter.number_of_blank_lines_at_beginning_of_method_body=0
org.eclipse.wst.jsdt.core.formatter.number_of_empty_lines_to_preserve=1
org.eclipse.wst.jsdt.core.formatter.put_empty_statement_on_new_line=true
org.eclipse.wst.jsdt.core.formatter.tabulation.char=space
org.eclipse.wst.jsdt.core.formatter.tabulation.size=4
org.eclipse.wst.jsdt.core.formatter.use_tabs_only_for_leading_indentations=false
org.eclipse.wst.jsdt.core.formatter.wrap_before_binary_operator=false
semanticValidation=disabled
