name: sandcastle-dev
on:
  push:
    branches:
      - 'cesium.com'
jobs:
  deploy:
    runs-on: ubuntu-latest
    env:
      PROD: true
      AWS_ACCESS_KEY_ID: ${{ secrets.PROD_AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.PROD_AWS_SECRET_ACCESS_KEY }}
      AWS_REGION: us-east-1
      BRANCH: ${{ github.ref_name }}
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      GITHUB_REPO: ${{ github.repository }}
      GITHUB_SHA: ${{ github.sha }}
    steps:
      - uses: actions/checkout@v5
      - name: install node 22
        uses: actions/setup-node@v4
        with:
          node-version: '22'
      - name: npm install
        run: npm install
      - name: build website release
        run: npm run website-release
      - name: build types
        run: npm run build-ts
      - name: build prod sandcastle
        run: npm run build-prod -w packages/sandcastle -- -l warn
      - name: deploy to dev-sandcastle.cesium.com
        if: ${{ env.AWS_ACCESS_KEY_ID != '' }}
        run: |
          aws s3 sync Build/Sandcastle2/ s3://cesium-dev-sandcastle-website/ --cache-control "public, max-age=1800" --delete
