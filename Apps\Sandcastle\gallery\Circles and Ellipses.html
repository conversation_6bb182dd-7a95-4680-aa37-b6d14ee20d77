<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="Draw circles, ellipses, and extruded volumes that conform to the surface of the globe."
    />
    <meta name="cesium-sandcastle-labels" content="Geometries" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer");

        const greenCircle = viewer.entities.add({
          position: Cesium.Cartesian3.fromDegrees(-111.0, 40.0, 150000.0),
          name: "Green circle at height with outline",
          ellipse: {
            semiMinorAxis: 300000.0,
            semiMajorAxis: 300000.0,
            height: 200000.0,
            material: Cesium.Color.GREEN,
            outline: true, // height must be set for outline to display
          },
        });

        const redEllipse = viewer.entities.add({
          position: Cesium.Cartesian3.fromDegrees(-103.0, 40.0),
          name: "Red ellipse on surface",
          ellipse: {
            semiMinorAxis: 250000.0,
            semiMajorAxis: 400000.0,
            material: Cesium.Color.RED.withAlpha(0.5),
          },
        });

        const blueEllipse = viewer.entities.add({
          position: Cesium.Cartesian3.fromDegrees(-95.0, 40.0, 100000.0),
          name: "Blue translucent, rotated, and extruded ellipse with outline",
          ellipse: {
            semiMinorAxis: 150000.0,
            semiMajorAxis: 300000.0,
            extrudedHeight: 200000.0,
            rotation: Cesium.Math.toRadians(45),
            material: Cesium.Color.BLUE.withAlpha(0.5),
            outline: true,
          },
        });

        viewer.zoomTo(viewer.entities);
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
