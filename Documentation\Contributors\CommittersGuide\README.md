# Committer's Guide

## Developing

- Make changes on a new branch, and open a pull request to merge into main.
- Sparingly, trivial changes such as rewording CHANGES.md or fixing a typo in a comment can be committed directly to main.

## Reviewing

- Review any pull request that interests you. Merge pull requests that you and other reviewers are confident in. Follow the [Code Review Guide](../CodeReviewGuide/README.md) and verify the contributor [submitted a CLA](../CodeReviewGuide/README.md#general).

## New Committers

- If a contributor has _significant and sustained_ contributions and should have commit access, propose <NAME_EMAIL>. Following the [Apache Way](http://community.apache.org/newcommitter.html), they will be given commit access with three yes votes and no no's over a week. To keep with the community spirit of the project, this is independent of affiliation; no one is entitled to commit access solely based on their affiliation. See Producing Open Source Software: [Money Can't Buy You Love](http://producingoss.com/en/money-vs-love.html) and [Committers](http://producingoss.com/en/committers.html).
- If a committer is inactive for one year, they lose commit access.
- Everyone with commit access to the main CesiumJS repo must enable [two-factor authentication](https://help.github.com/articles/about-two-factor-authentication) on their GitHub account.
