/* ContentPane 
 *	
 * .dijitContentPane
 * 	set padding for basic content pane
 * 	
 * Nested layouts:
 * 
 * .dijitTabContainerTop-dijitContentPane,
 * .dijitTabContainerLeft-dijitContentPane,
 * .dijitTabContainerBottom-dijitContentPane,
 * .dijitTabContainerRight-dijitContentPane
 * 	set background-color and padding of ContentPanes nested within TabContainer (can do top, left, bottom, or right) or Accordion Container
 *
 * .dijitAccordionContainer-dijitContentPane
 * 	set background-color and padding of ContentPane nested within Accordion
 *
 * .dijitSplitContainer-dijitContentPane, 
 *	set background-color and padding of ContentPane nested within a SplitContainer 
 *
 * .dijitBorderContainer-dijitContentPane
 *	set background-color and padding of ContentPane nested within a BorderContainer 
 */
.claro .dijitContentPane {
  padding: 8px;
}
/* nested layouts */
.claro .dijitTabContainerTop-dijitContentPane,
.claro .dijitTabContainerLeft-dijitContentPane,
.claro .dijitTabContainerBottom-dijitContentPane,
.claro .dijitTabContainerRight-dijitContentPane,
.claro .dijitAccordionContainer-dijitContentPane {
  background-color: #ffffff;
  padding: 8px;
}
.claro .dijitSplitContainer-dijitContentPane,
.claro .dijitBorderContainer-dijitContentPane {
  background-color: #ffffff;
  padding: 8px;
}
