<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="Toggle High Dynamic Range (HDR)." />
    <meta name="cesium-sandcastle-labels" content="Post Processing" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);

      #toolbar {
        background: rgba(42, 42, 42, 0.8);
        padding: 4px;
        border-radius: 4px;
      }

      #toolbar input {
        vertical-align: middle;
        padding-top: 2px;
        padding-bottom: 2px;
      }
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar">
      <div id="hdr-toggle"></div>
      <span>Tonemap</span>
      <span id="tonemap-select"></span>
      <br />
      <span>Exposure</span>
      <input
        type="range"
        min="0.1"
        max="10.0"
        step="0.1"
        data-bind="value: exposure, valueUpdate: 'input'"
      />
      <input type="text" size="5" data-bind="value: exposure" />
    </div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer", {
          terrain: Cesium.Terrain.fromWorldTerrain(),
          shadows: true,
          timeline: false,
          animation: false,
          geocoder: false,
          sceneModePicker: false,
          baseLayerPicker: false,
        });

        if (!viewer.scene.highDynamicRangeSupported) {
          window.alert("This browser does not support high dynamic range.");
        }

        viewer.scene.highDynamicRange = true;

        Sandcastle.addToggleButton(
          "HDR",
          true,
          function (checked) {
            viewer.scene.highDynamicRange = checked;
          },
          "hdr-toggle",
        );

        const toneMapOptions = [
          {
            text: "PBR Neutral",
            onselect: function () {
              viewer.scene.postProcessStages.tonemapper = Cesium.Tonemapper.PBR_NEUTRAL;
            },
          },
          {
            text: "Aces",
            onselect: function () {
              viewer.scene.postProcessStages.tonemapper = Cesium.Tonemapper.ACES;
            },
          },
          {
            text: "Reinhard",
            onselect: function () {
              viewer.scene.postProcessStages.tonemapper = Cesium.Tonemapper.REINHARD;
            },
          },
          {
            text: "Modified_Reinhard",
            onselect: function () {
              viewer.scene.postProcessStages.tonemapper =
                Cesium.Tonemapper.MODIFIED_REINHARD;
            },
          },
          {
            text: "Filmic",
            onselect: function () {
              viewer.scene.postProcessStages.tonemapper = Cesium.Tonemapper.FILMIC;
            },
          },
        ];
        Sandcastle.addDefaultToolbarMenu(toneMapOptions, "tonemap-select");

        const viewModel = {
          exposure: 1,
        };
        // Convert the viewModel members into knockout observables.
        Cesium.knockout.track(viewModel);
        // Bind the viewModel to the DOM elements of the UI that call for it.
        const toolbar = document.getElementById("toolbar");
        Cesium.knockout.applyBindings(viewModel, toolbar);

        Cesium.knockout
          .getObservable(viewModel, "exposure")
          .subscribe(function (newValue) {
            viewer.scene.postProcessStages.exposure = Number.parseFloat(newValue);
          });

        const url = "../../SampleData/models/DracoCompressed/CesiumMilkTruck.gltf";
        const position = Cesium.Cartesian3.fromRadians(
          -1.9516424279517286,
          0.6322397098422969,
          1239.0006814631095,
        );
        const heading = Cesium.Math.toRadians(-15.0);
        const pitch = 0;
        const roll = 0;
        const hpr = new Cesium.HeadingPitchRoll(heading, pitch, roll);
        const orientation = Cesium.Transforms.headingPitchRollQuaternion(position, hpr);
        const scale = 10.0;

        const entity = viewer.entities.add({
          name: url,
          position: position,
          orientation: orientation,
          model: {
            uri: url,
            scale: scale,
          },
        });

        // set up canyon view
        viewer.scene.camera.setView({
          destination: new Cesium.Cartesian3(
            -1915097.7863741855,
            -4783356.851539908,
            3748887.43462683,
          ),
          orientation: new Cesium.HeadingPitchRoll(
            6.166004548388564,
            -0.043242401760068994,
            0.002179961955988574,
          ),
          endTransform: Cesium.Matrix4.IDENTITY,
        });
        // set time so the sun is overhead
        viewer.clock.currentTime = new Cesium.JulianDate(2460550, 21637);

        viewer.scene.debugShowFramesPerSecond = true;
        // override the default home location to return to the start
        viewer.homeButton.viewModel.command.beforeExecute.addEventListener((e) => {
          e.cancel = true;
          viewer.scene.camera.setView({
            destination: new Cesium.Cartesian3(
              -1915097.7863741855,
              -4783356.851539908,
              3748887.43462683,
            ),
            orientation: new Cesium.HeadingPitchRoll(
              6.166004548388564,
              -0.043242401760068994,
              0.002179961955988574,
            ),
            endTransform: Cesium.Matrix4.IDENTITY,
          });
        });
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
