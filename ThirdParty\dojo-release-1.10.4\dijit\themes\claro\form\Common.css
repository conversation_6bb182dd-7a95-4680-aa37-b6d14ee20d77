/* claro/form/Common.css */
/*========================= common css =========================*/
/* 'dijitTextBox' refers to 'dijit(TextBox|DateTextBox|CurrencyTextBox|...)' */
.claro .dijitTextBox,
.claro .dijitInputInner {
  color: #000000;
}
.claro .dijitValidationTextBoxError .dijitValidationContainer {
  background-color: #d46464;
  background-image: url("../form/images/error.png");
  background-position: top center;
  border: solid #d46464 0;
  width: 9px;
}
.claro .dijitTextBoxError .dijitValidationContainer {
  border-left-width: 1px;
}
.claro .dijitValidationTextBoxError .dijitValidationIcon {
  width: 0;
  background-color: transparent;
  /* so the INPUT doesn't obscure the border in rtl+a11y */

}
/* Padding for the input area of TextBox based widgets, and corresponding padding for the
 * down arrow button and the placeholder.   placeholder is explicitly listed  because
 * dijitPlaceHolder is absolutely positioned, so padding set on dijitInputField
 * won't affect it
 */
.claro .dijitTextArea,
.claro .dijitInputField .dijitPlaceHolder {
  padding: 2px;
}
.claro .dijitSelect .dijitInputField,
.claro .dijitTextBox .dijitInputField {
  padding: 1px 2px;
}
.dj_gecko .claro .dijitTextBox .dijitInputInner,
.dj_webkit .claro .dijitTextBox .dijitInputInner {
  padding-left: 1px;
  padding-right: 1px;
}
.claro .dijitSelect,
.claro .dijitSelect .dijitButtonContents,
.claro .dijitTextBox,
.claro .dijitTextBox .dijitButtonNode {
  /* color for (outer) border on *TextBox widgets, and border between input and buttons on ComboBox and Spinner */

  border-color: #b5bcc7;
  -webkit-transition-property: background-color, border;
  -moz-transition-property: background-color, border;
  transition-property: background-color, border;
  -webkit-transition-duration: 0.35s;
  -moz-transition-duration: 0.35s;
  transition-duration: 0.35s;
}
.claro .dijitSelect,
.claro .dijitTextBox {
  background-color: #ffffff;
}
/* hover */
.claro .dijitSelectHover,
.claro .dijitSelectHover .dijitButtonContents,
.claro .dijitTextBoxHover,
.claro .dijitTextBoxHover .dijitButtonNode {
  border-color: #759dc0;
  -webkit-transition-duration: 0.25s;
  -moz-transition-duration: 0.25s;
  transition-duration: 0.25s;
}
.claro .dijitTextBoxHover {
  background-color: #e5f2fe;
  background-image: -moz-linear-gradient(rgba(127, 127, 127, 0.2) 0%, rgba(127, 127, 127, 0) 2px);
  background-image: -webkit-linear-gradient(rgba(127, 127, 127, 0.2) 0%, rgba(127, 127, 127, 0) 2px);
  background-image: -o-linear-gradient(rgba(127, 127, 127, 0.2) 0%, rgba(127, 127, 127, 0) 2px);
  background-image: linear-gradient(rgba(127, 127, 127, 0.2) 0%, rgba(127, 127, 127, 0) 2px);
}
/* error state */
.claro .dijitSelectError,
.claro .dijitSelectError .dijitButtonContents,
.claro .dijitTextBoxError,
.claro .dijitTextBoxError .dijitButtonNode {
  border-color: #d46464;
}
/* focused state */
.claro .dijitSelectFocused,
.claro .dijitSelectFocused .dijitButtonContents,
.claro .dijitTextBoxFocused,
.claro .dijitTextBoxFocused .dijitButtonNode {
  border-color: #759dc0;
  -webkit-transition-duration: 0.1s;
  -moz-transition-duration: 0.1s;
  transition-duration: 0.1s;
}
.claro .dijitTextBoxFocused {
  background-color: #ffffff;
  background-image: -moz-linear-gradient(rgba(127, 127, 127, 0.2) 0%, rgba(127, 127, 127, 0) 2px);
  background-image: -webkit-linear-gradient(rgba(127, 127, 127, 0.2) 0%, rgba(127, 127, 127, 0) 2px);
  background-image: -o-linear-gradient(rgba(127, 127, 127, 0.2) 0%, rgba(127, 127, 127, 0) 2px);
  background-image: linear-gradient(rgba(127, 127, 127, 0.2) 0%, rgba(127, 127, 127, 0) 2px);
}
.claro .dijitTextBoxFocused .dijitInputContainer {
  background: #ffffff;
}
.claro .dijitSelectErrorFocused,
.claro .dijitSelectErrorFocused .dijitButtonContents,
.claro .dijitTextBoxErrorFocused,
.claro .dijitTextBoxErrorFocused .dijitButtonNode {
  border-color: #ce5050;
}
/* disabled state */
.claro .dijitSelectDisabled,
.claro .dijitSelectDisabled .dijitButtonContents,
.claro .dijitTextBoxDisabled,
.claro .dijitTextBoxDisabled .dijitButtonNode {
  border-color: #d3d3d3;
}
.claro .dijitSelectDisabled,
.claro .dijitTextBoxDisabled,
.claro .dijitTextBoxDisabled .dijitInputContainer {
  background-color: #efefef;
  background-image: none;
}
.claro .dijitSelectDisabled,
.claro .dijitTextBoxDisabled,
.claro .dijitTextBoxDisabled .dijitInputInner {
  color: #818181;
}
.dj_webkit .claro .dijitDisabled input {
  /* because WebKit lightens disabled input/textarea no matter what color you specify */

  color: #757575;
}
.dj_webkit .claro textarea.dijitTextAreaDisabled {
  /* because WebKit lightens disabled input/textarea no matter what color you specify */

  color: #1b1b1b;
}
/*========================= for special widgets =========================*/
/* Input boxes with an arrow (for a drop down) */
.claro .dijitSelect .dijitArrowButtonInner,
.claro .dijitComboBox .dijitArrowButtonInner {
  background-image: url("../form/images/commonFormArrows.png");
  background-position: -35px 53%;
  background-repeat: no-repeat;
  margin: 0;
  width: 16px;
}
.claro .dijitComboBox .dijitArrowButtonInner {
  border: 1px solid #ffffff;
}
.claro .dijitToolbar .dijitComboBox .dijitArrowButtonInner {
  border: none;
}
.claro .dijitToolbar .dijitComboBox .dijitArrowButtonInner {
  border: none;
}
/* Add 1px vertical padding to the <input> where user types and the validation icon,
   to match the 1px border on arrow button */
.claro .dijitSelectLabel,
.claro .dijitTextBox .dijitInputInner,
.claro .dijitValidationTextBox .dijitValidationContainer {
  padding: 1px 0;
}
.claro .dijitComboBox .dijitButtonNode {
  background-color: #efefef;
  background-image: url("../images/standardGradient.png");
  background-repeat: repeat-x;
  background-image: -moz-linear-gradient(rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0) 100%);
  background-image: -webkit-linear-gradient(rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0) 100%);
  background-image: -o-linear-gradient(rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0) 100%);
  background-image: linear-gradient(rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0) 100%);
  _background-image: none;
}
/* Arrow "hover" effect:
 * The arrow button should change color whenever the mouse is in a position such that clicking it
 * will toggle the drop down.   That's either (1) anywhere over the ComboBox or (2) over the arrow
 * button, depending on the openOnClick setting for the widget.
 */
.claro .dijitComboBoxOpenOnClickHover .dijitButtonNode,
.claro .dijitComboBox .dijitDownArrowButtonHover,
.claro .dijitComboBoxFocused .dijitArrowButton {
  background-color: #abd6ff;
}
.claro .dijitComboBoxOpenOnClickHover .dijitArrowButtonInner,
.claro .dijitComboBox .dijitDownArrowButtonHover .dijitArrowButtonInner {
  background-position: -70px 53%;
}
/* Arrow Button change when drop down is open */
.claro .dijitComboBox .dijitHasDropDownOpen {
  background-color: #7dbdfa;
  background-image: url("../images/activeGradient.png");
  background-repeat: repeat-x;
  background-image: -moz-linear-gradient(rgba(190, 190, 190, 0.98) 0px, rgba(255, 255, 255, 0.65) 3px, rgba(255, 255, 255, 0) 100%);
  background-image: -webkit-linear-gradient(rgba(190, 190, 190, 0.98) 0px, rgba(255, 255, 255, 0.65) 3px, rgba(255, 255, 255, 0) 100%);
  background-image: -o-linear-gradient(rgba(190, 190, 190, 0.98) 0px, rgba(255, 255, 255, 0.65) 3px, rgba(255, 255, 255, 0) 100%);
  background-image: linear-gradient(rgba(190, 190, 190, 0.98) 0px, rgba(255, 255, 255, 0.65) 3px, rgba(255, 255, 255, 0) 100%);
  _background-image: none;
  padding: 1px;
}
.dj_iequirks .claro .dijitComboBox .dijitHasDropDownOpen {
  padding: 1px 0;
}
.claro .dijitComboBox .dijitHasDropDownOpen .dijitArrowButtonInner {
  background-position: -70px 53%;
  border: 0 none;
}
/* disabled state */
.claro div.dijitComboBoxDisabled .dijitArrowButtonInner {
  /* specific selector set to override background-position setting from Button.js
	 * (.claro .dijitComboBoxDisabled .dijitArrowButtonInner) */

  background-position: 0 50%;
  background-color: #efefef;
}
/*========================= hacks for browsers =========================*/
/* it seems the input[type="hidden"] has a height (16px) too... this may cause the widget's height calculate error */
.dj_ff3 .claro .dijitInputField input[type="hidden"] {
  display: none;
  height: 0;
  width: 0;
}
.dj_borderbox .claro .dijitComboBox .dijitHasDropDownOpen .dijitArrowButtonInner {
  width: 18px;
}
.dj_borderbox .claro .dijitComboBoxFocused .dijitHasDropDownOpen .dijitArrowButtonInner {
  width: 16px;
}
