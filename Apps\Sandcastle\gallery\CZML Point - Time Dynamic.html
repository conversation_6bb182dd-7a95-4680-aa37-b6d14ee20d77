<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="CZML Point - Time Dynamic" />
    <meta name="cesium-sandcastle-labels" content="CZML" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>

    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const czml = [
          {
            id: "document",
            name: "CZML Point - Time Dynamic",
            version: "1.0",
          },
          {
            id: "point",
            availability: "2012-08-04T16:00:00Z/2012-08-04T16:05:00Z",
            position: {
              epoch: "2012-08-04T16:00:00Z",
              cartographicDegrees: [
                0, -70, 20, 150000, 100, -80, 44, 150000, 200, -90, 18, 150000, 300, -98,
                52, 150000,
              ],
            },
            point: {
              color: {
                rgba: [255, 255, 255, 128],
              },
              outlineColor: {
                rgba: [255, 0, 0, 128],
              },
              outlineWidth: 3,
              pixelSize: 15,
            },
          },
        ];

        const viewer = new Cesium.Viewer("cesiumContainer", {
          shouldAnimate: true,
        });

        viewer.dataSources.add(Cesium.CzmlDataSource.load(czml));

        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
