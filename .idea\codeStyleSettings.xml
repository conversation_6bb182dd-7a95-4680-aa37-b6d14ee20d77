<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ProjectCodeStyleSettingsManager">
    <option name="PER_PROJECT_SETTINGS">
      <value>
        <JSCodeStyleSettings>
          <option name="SPACE_BEFORE_PROPERTY_COLON" value="true" />
          <option name="SPACE_BEFORE_FUNCTION_LEFT_PARENTH" value="false" />
        </JSCodeStyleSettings>
        <XML>
          <option name="XML_LEGACY_SETTINGS_IMPORTED" value="true" />
        </XML>
        <codeStyleSettings language="JavaScript">
          <option name="KEEP_BLANK_LINES_IN_CODE" value="1" />
          <option name="ALIGN_MULTILINE_BINARY_OPERATION" value="true" />
          <option name="ALIGN_MULTILINE_TERNARY_OPERATION" value="true" />
          <option name="ALIGN_MULTILINE_ARRAY_INITIALIZER_EXPRESSION" value="true" />
          <option name="METHOD_PARAMETERS_LPAREN_ON_NEXT_LINE" value="true" />
          <option name="IF_BRACE_FORCE" value="3" />
        </codeStyleSettings>
      </value>
    </option>
    <option name="USE_PER_PROJECT_SETTINGS" value="true" />
  </component>
</project>