<fieldset>
	<legend data-dojo-attach-event="ondijitclick:_onTitleClick, onkeydown:_onTitleKey"
			data-dojo-attach-point="titleBarNode, titleNode">
		<span data-dojo-attach-point="arrowNode" class="dijitInline dijitArrowNode" role="presentation"></span
		><span data-dojo-attach-point="arrowNodeInner" class="dijitArrowNodeInner"></span
		><span data-dojo-attach-point="titleNode, focusNode" class="dijitFieldsetLegendNode" id="${id}_titleNode"></span>
	</legend>
	<div class="dijitFieldsetContentOuter" data-dojo-attach-point="hideNode" role="presentation">
		<div class="dijitReset" data-dojo-attach-point="wipeNode" role="presentation">
			<div class="dijitFieldsetContentInner" data-dojo-attach-point="containerNode" role="region"
				 	id="${id}_pane" aria-labelledby="${id}_titleNode">
				<!-- nested divs because wipeIn()/wipeOut() doesn't work right on node w/padding etc.  Put padding on inner div. -->
			</div>
		</div>
	</div>
</fieldset>
