{"type": "Topology", "transform": {"scale": [0.0035892802775563276, 0.0005250691027211737], "translate": [-179.14350338367416, 18.906117143691233]}, "objects": {"states": {"type": "GeometryCollection", "geometries": [{"type": "MultiPolygon", "arcs": [[[0]], [[1]], [[2, 3, 4, 5, 6, 7, 8, 9]]], "id": "MA", "properties": {"name": "Massachusetts", "Statehood": "Feb. 6, 1788", "Population": 6692824}}, {"type": "Polygon", "arcs": [[10, 11, 12, 13, 14, 15]], "id": "MN", "properties": {"name": "Minnesota", "Statehood": "May 11, 1858", "Population": 5420380}}, {"type": "Polygon", "arcs": [[16, 17, 18, 19, 20]], "id": "MT", "properties": {"name": "Montana", "Statehood": "Nov. 8, 1889", "Population": 1015165}}, {"type": "Polygon", "arcs": [[21, -21, 22, -15]], "id": "ND", "properties": {"name": "North Dakota", "Statehood": "Nov. 2, 1889", "Population": 723393}}, {"type": "MultiPolygon", "arcs": [[[23]], [[24]], [[25]], [[26]], [[27]], [[28]], [[29]], [[30]]], "id": "HI", "properties": {"name": "Hawaii", "Statehood": "Aug. 21, 1959", "Population": 1404054}}, {"type": "Polygon", "arcs": [[31, 32, 33, 34, 35, 36, -19]], "id": "ID", "properties": {"name": "Idaho", "Statehood": "July 3, 1890", "Population": 1612136}}, {"type": "MultiPolygon", "arcs": [[[37]], [[38]], [[39]], [[40]], [[41]], [[42]], [[43]], [[-36, 44, 45]]], "id": "WA", "properties": {"name": "Washington", "Statehood": "Nov. 11, 1889", "Population": 6971406}}, {"type": "Polygon", "arcs": [[46, 47, 48, 49, 50]], "id": "AZ", "properties": {"name": "Arizona", "Statehood": "Feb. 14, 1912", "Population": 6626624}}, {"type": "MultiPolygon", "arcs": [[[51]], [[52]], [[53]], [[54]], [[-49, 55, 56, 57]]], "id": "CA", "properties": {"name": "California", "Statehood": "Sept. 9, 1850", "Population": 38332521}}, {"type": "Polygon", "arcs": [[58, 59, 60, 61, 62, 63]], "id": "CO", "properties": {"name": "Colorado", "Statehood": "Aug. 1, 1876", "Population": 5268367}}, {"type": "Polygon", "arcs": [[-50, -58, 64, -34, 65]], "id": "NV", "properties": {"name": "Nevada", "Statehood": "Oct. 31, 1864", "Population": 2790136}}, {"type": "Polygon", "arcs": [[66, 67, -47, -61, 68]], "id": "NM", "properties": {"name": "New Mexico", "Statehood": "Jan. 6, 1912", "Population": 2085287}}, {"type": "MultiPolygon", "arcs": [[[-45, -35, -65, -57, 69]]], "id": "OR", "properties": {"name": "Oregon", "Statehood": "Feb. 14, 1859", "Population": 3930065}}, {"type": "Polygon", "arcs": [[70, -62, -51, -66, -33]], "id": "UT", "properties": {"name": "Utah", "Statehood": "Jan. 4, 1896", "Population": 2900872}}, {"type": "Polygon", "arcs": [[71, -63, -71, -32, -18, 72]], "id": "WY", "properties": {"name": "Wyoming", "Statehood": "July 10, 1890", "Population": 582658}}, {"type": "Polygon", "arcs": [[73, 74, 75, 76, 77, 78]], "id": "AR", "properties": {"name": "Arkansas", "Statehood": "June 15, 1836", "Population": 2959373}}, {"type": "Polygon", "arcs": [[79, 80, 81, 82, -13, 83]], "id": "IA", "properties": {"name": "Iowa", "Statehood": "Dec. 28, 1846", "Population": 3090416}}, {"type": "Polygon", "arcs": [[84, -59, 85, 86]], "id": "KS", "properties": {"name": "Kansas", "Statehood": "Jan. 29, 1861", "Population": 2893957}}, {"type": "Polygon", "arcs": [[87, 88, 89, 90, 91, -79, 92, -87, 93, -81]], "id": "MO", "properties": {"name": "Missouri", "Statehood": "Aug. 10, 1821", "Population": 6044171}}, {"type": "Polygon", "arcs": [[-82, -94, -86, -64, -72, 94]], "id": "NE", "properties": {"name": "Nebraska", "Statehood": "Mar. 1, 1867", "Population": 1868516}}, {"type": "Polygon", "arcs": [[-78, 95, -69, -60, -85, -93]], "id": "OK", "properties": {"name": "Oklahoma", "Statehood": "Nov. 16, 1907", "Population": 3850568}}, {"type": "Polygon", "arcs": [[-14, -83, -95, -73, -17, -22]], "id": "SD", "properties": {"name": "South Dakota", "Statehood": "Nov. 2, 1889", "Population": 844877}}, {"type": "MultiPolygon", "arcs": [[[96]], [[97]], [[98, 99, -76, 100]]], "id": "LA", "properties": {"name": "Louisiana", "Statehood": "Apr. 30, 1812", "Population": 4625470}}, {"type": "MultiPolygon", "arcs": [[[101]], [[102]], [[103]], [[104]], [[105]], [[-77, -100, 106, -67, -96]]], "id": "TX", "properties": {"name": "Texas", "Statehood": "Dec. 29, 1845", "Population": 26448193}}, {"type": "Polygon", "arcs": [[107, 108, 109, -7]], "id": "CT", "properties": {"name": "Connecticut", "Statehood": "Jan. 9, 1788", "Population": 3596080}}, {"type": "Polygon", "arcs": [[110, -10, 111, 112, 113]], "id": "NH", "properties": {"name": "New Hampshire", "Statehood": "June 21, 1788", "Population": 1323459}}, {"type": "MultiPolygon", "arcs": [[[114]], [[115, -4]], [[116, -108, -6]]], "id": "RI", "properties": {"name": "Rhode Island", "Statehood": "May 29, 1790", "Population": 1051511}}, {"type": "Polygon", "arcs": [[-9, 117, 118, -112]], "id": "VT", "properties": {"name": "Vermont", "Statehood": "Mar. 4, 1791", "Population": 626630}}, {"type": "MultiPolygon", "arcs": [[[119]], [[120, 121, 122, 123, 124]]], "id": "AL", "properties": {"name": "Alabama", "Statehood": "Dec. 14, 1819", "Population": 4833722}}, {"type": "MultiPolygon", "arcs": [[[125]], [[126]], [[127]], [[128]], [[129]], [[130]], [[131, -122, 132]]], "id": "FL", "properties": {"name": "Florida", "Statehood": "Mar. 3, 1845", "Population": 19552860}}, {"type": "MultiPolygon", "arcs": [[[133]], [[134]], [[135, -133, -121, 136, 137, 138]]], "id": "GA", "properties": {"name": "Georgia", "Statehood": "Jan. 2, 1788", "Population": 9992167}}, {"type": "MultiPolygon", "arcs": [[[-124, 139, -101, -75, 140]]], "id": "MS", "properties": {"name": "Mississippi", "Statehood": "Dec. 10, 1817", "Population": 2991207}}, {"type": "MultiPolygon", "arcs": [[[141, -139, 142]]], "id": "SC", "properties": {"name": "South Carolina", "Statehood": "May 23, 1788", "Population": 4774839}}, {"type": "Polygon", "arcs": [[143, 144, 145, -88, -80, 146]], "id": "IL", "properties": {"name": "Illinois", "Statehood": "Dec. 3, 1818", "Population": 12882135}}, {"type": "Polygon", "arcs": [[147, 148, 149, -145]], "id": "IN", "properties": {"name": "Indiana", "Statehood": "Dec. 11, 1816", "Population": 6570902}}, {"type": "MultiPolygon", "arcs": [[[150, 151, 152, -89, -146, -150, 153]]], "id": "KY", "properties": {"name": "Kentucky", "Statehood": "June 1, 1792", "Population": 4395295}}, {"type": "MultiPolygon", "arcs": [[[154]], [[155, 156]], [[157, 158, 159, 160]], [[161, -143, -138, 162, 163]]], "id": "NC", "properties": {"name": "North Carolina", "Statehood": "Nov. 21, 1789", "Population": 9848060}}, {"type": "Polygon", "arcs": [[164, -154, -149, 165, 166, 167]], "id": "OH", "properties": {"name": "Ohio", "Statehood": "Mar. 1, 1803", "Population": 11570808}}, {"type": "Polygon", "arcs": [[168, -163, -137, -125, -141, -74, -92, -170, -90, -153]], "id": "TN", "properties": {"name": "Tennessee", "Statehood": "June 1, 1796", "Population": 6495978}}, {"type": "MultiPolygon", "arcs": [[[170, 171, 172, -156, 173, -161, 174, -164, -169, -152, 175]]], "id": "VA", "properties": {"name": "Virginia", "Statehood": "June 25, 1788", "Population": 8260405}}, {"type": "Polygon", "arcs": [[176, -147, -84, -12]], "id": "WI", "properties": {"name": "Wisconsin", "Statehood": "May 29, 1848", "Population": 5742713}}, {"type": "Polygon", "arcs": [[177, -176, -151, -165, 178]], "id": "WV", "properties": {"name": "West Virginia", "Statehood": "June 20, 1863", "Population": 1854304}}, {"type": "Polygon", "arcs": [[179, 180, 181, 182, 183]], "id": "DE", "properties": {"name": "Delaware", "Statehood": "Dec. 7, 1787", "Population": 925749}}, {"type": "Polygon", "arcs": [[184, -172, 185]], "id": "DC", "properties": {"name": "District of Columbia", "Statehood": "July 16, 1790", "Population": 646449}}, {"type": "MultiPolygon", "arcs": [[[-183, 186, -186, -171, -178, 187]]], "id": "MD", "properties": {"name": "Maryland", "Statehood": "Apr. 28, 1788", "Population": 5928814}}, {"type": "MultiPolygon", "arcs": [[[188, 189, 190]]], "id": "NJ", "properties": {"name": "New Jersey", "Statehood": "Dec. 18, 1787", "Population": 8899339}}, {"type": "MultiPolygon", "arcs": [[[191]], [[192]], [[-118, -8, -110, 193, -190, 194, 195]]], "id": "NY", "properties": {"name": "New York", "Statehood": "July 26, 1788", "Population": 19651127}}, {"type": "Polygon", "arcs": [[-189, 196, -184, -188, -179, -168, 197, -195]], "id": "PA", "properties": {"name": "Pennsylvania", "Statehood": "Dec. 12, 1787", "Population": 12773801}}, {"type": "MultiPolygon", "arcs": [[[198]], [[199]], [[200]], [[-114, 201]]], "id": "ME", "properties": {"name": "Maine", "Statehood": "Mar. 15, 1820", "Population": 1328302}}, {"type": "Polygon", "arcs": [[-166, -148, -144, -177, -11, 202]], "id": "MI", "properties": {"name": "Michigan", "Statehood": "Jan. 26, 1837", "Population": 9895622}}, {"type": "MultiPolygon", "arcs": [[[203]], [[204]], [[205]], [[206]], [[207]], [[208]], [[209]], [[210]], [[211]], [[212]], [[213]], [[214]], [[215]], [[216]], [[217]], [[218]], [[219]], [[220]], [[221]], [[222]], [[223]], [[224]], [[225]], [[226]], [[227]], [[228]], [[229]], [[230]], [[231]], [[232]], [[233]], [[234]], [[235]], [[236]], [[237]], [[238]], [[239]], [[240]], [[241]], [[242]], [[243]], [[244]], [[245]], [[246]], [[247]], [[248]], [[249]], [[250]], [[251]], [[252]], [[253]], [[254]], [[255]], [[256]], [[257]], [[258]], [[259]], [[260]], [[261]], [[262]], [[263]], [[264]], [[265]], [[266]], [[267]], [[268]], [[269]], [[270]], [[271]], [[272]], [[273]], [[274]], [[275]], [[276]], [[277]], [[278]], [[279]], [[280]], [[281]], [[282]], [[283]], [[284]], [[285]], [[286]], [[287]], [[288]]], "id": "AK", "properties": {"name": "Alaska", "Statehood": "Jan. 3, 1959", "Population": 735132}}]}}, "arcs": [[[30403, 42783], [17, -180], [-50, -54], [-39, 115], [50, -34], [22, 153]], [[30242, 42976], [25, -207], [-77, -16], [52, 223]], [[30181, 45653], [11, -306], [36, -213], [-60, -110], [7, -91], [-38, -141], [-20, -197], [27, -151], [14, 145], [51, -203], [23, -227], [-6, -259], [25, -3], [9, -264], [72, -193], [76, 193], [4, 185], [-23, -39], [5, 278], [19, -164], [14, -305], [-2, -226], [-136, -80], [-21, -130], [-43, -82], [7, 362], [-24, 67], [-16, -190], [-36, -13], [-6, -170], [-54, -71]], [[30096, 43055], [-3, 282], [-17, 30]], [[30076, 43367], [23, 229], [-36, -167]], [[30063, 43429], [-29, 179], [-12, 404], [-116, -10]], [[29906, 44002], [0, 22], [-266, 21], [-202, 41]], [[29438, 44086], [-7, 47], [67, 1270]], [[29498, 45403], [223, -32]], [[29721, 45371], [323, -54], [106, 354], [31, -18]], [[24976, 55400], [-128, -1350]], [[24848, 54050], [-194, 41], [-227, -731], [-138, -373], [-41, 78], [-29, -200], [-21, -2], [-1, -1104], [-19, -106], [-95, -218], [-19, -207], [-31, -169], [0, -266], [37, -32], [23, -217], [-26, -309], [3, -255], [-15, -136], [13, -289], [-18, -321], [66, -285], [63, -92], [24, -178], [47, -55], [43, -193], [31, -294], [49, -206], [71, -114], [33, -199], [11, -266], [0, -511]], [[24488, 46841], [-272, 0], [-244, -1], [-391, 0], [-271, 0], [-272, 0]], [[23038, 46840], [0, 3421], [-31, 185], [-40, 53], [-43, 340], [9, 103], [54, 208], [22, 340]], [[23009, 51490], [-10, 687], [-43, 373], [-15, 562], [12, 242], [-19, 129], [-9, 1178], [-43, 515], [-32, 558], [2, 492], [-10, 201], [16, 222], [-35, 651]], [[22823, 57300], [203, 0], [368, 0], [4, 718], [90, -116], [35, -751], [3, -237], [62, -170], [47, 25], [22, -118], [99, -31], [33, -227], [74, 49], [11, 105], [54, 63], [78, -24], [92, -155], [-15, -174], [49, 1], [40, -407], [22, 151], [73, 45], [14, -165], [63, -116], [20, -170], [33, -95], [83, 32], [99, 309], [23, -13], [15, -228], [71, -31], [98, 60], [30, -28], [47, -219], [39, 60], [74, -43]], [[20923, 51490], [2, -1794], [-5, 0]], [[20920, 49696], [-419, 0], [-303, 0], [-243, 0], [-364, 0], [-364, 0], [-256, 0], [0, -957]], [[18971, 48739], [-43, 176], [-32, 283], [-37, -32], [-15, -242], [15, -81], [-64, 26], [-32, -72], [-32, 68], [-47, -47], [-42, 57], [-37, -195], [-94, 71], [-34, -130], [5, -80], [-52, 127], [-14, 366], [-27, 236], [-55, 41], [-40, 250], [14, 122], [-23, 250], [-57, 360], [-25, 412], [4, 134], [-58, 183], [-31, -240], [-63, -162], [-58, 166], [14, 253], [-15, 149], [45, 214], [-23, 211], [11, 352], [-6, 159], [32, 488], [4, 255], [-68, -40], [-14, 131], [-80, 245], [-3, 155], [-38, 141], [-81, 518], [-45, 74], [-23, 154], [-37, 91], [21, 64], [-24, 154], [15, 119], [-12, 165], [-91, 578], [0, 1884]], [[17579, 57300], [426, 0], [244, 0], [307, 0], [245, 0], [490, 0], [428, 0], [368, 0], [490, 0], [345, 0]], [[20922, 57300], [1, -362], [0, -5448]], [[23009, 51490], [-454, 0], [-260, 0], [-324, 0], [-454, 0], [-325, 0], [-269, 0]], [[20922, 57300], [389, 0], [307, 0], [490, 0], [245, 0], [470, 0]], [[6558, 2346], [76, -175], [46, -190], [21, -165], [2, -253], [17, 36], [20, -257], [37, -250], [-17, -127], [-60, -242], [-56, -51], [-71, -307], [0, -99], [-34, -266], [-58, 233], [-11, 210], [9, 372], [-26, 521], [-22, 235], [37, 262], [28, 289], [-15, 160], [3, 301], [37, -35], [37, -202]], [[6299, 3095], [-47, -12], [39, 138], [8, -126]], [[6192, 3482], [-18, 48], [-2, 165], [-18, 124], [44, 4], [22, -138], [-5, -148], [-23, -55]], [[6283, 4041], [31, -235], [51, 92], [49, -223], [33, -68], [0, -217], [-36, -118], [-47, -83], [-30, 13], [-15, 369], [-45, 48], [-20, 207], [5, 138], [24, 77]], [[6100, 4410], [97, -108], [54, -16], [-45, -198], [-57, 89], [-57, -24], [8, 257]], [[5919, 5127], [16, -261], [29, -107], [25, -189], [-42, -98], [-49, 151], [-34, -76], [-37, 455], [-14, 81], [44, 21], [30, 220], [14, 7], [18, -204]], [[5277, 5480], [-9, 173], [49, 239], [-7, -202], [-33, -210]], [[5504, 6328], [26, -157], [-11, -343], [-29, -187], [-46, 44], [-45, 165], [-6, 167], [18, 174], [45, 145], [48, -8]], [[18971, 48739], [0, -4757]], [[18971, 43982], [-417, 0], [-416, 1]], [[18138, 43983], [-156, -1], [-362, 0], [-311, 0]], [[17309, 43982], [-1, 3400], [35, 746], [-22, 174], [-63, 56], [5, 412], [17, 77], [25, 341], [32, 132], [23, 223], [3, 221], [29, 220], [19, 374], [52, 446], [-18, 323], [-65, 165], [-34, 311]], [[17346, 51603], [-20, 176], [13, 131], [-38, 410], [1, 147], [1, 4833]], [[17303, 57300], [276, 0]], [[15681, 53844], [-17, 97], [20, 132], [-3, -229]], [[15803, 54284], [-31, -99], [12, 238], [19, -139]], [[15783, 54651], [-23, 0], [4, 213], [19, -213]], [[15757, 56154], [20, -132], [-59, -168], [37, -54], [13, -358], [9, 139], [46, -265], [-25, -77], [-10, 146], [-33, 73], [-8, 263], [-37, 165], [28, 257], [19, 11]], [[15684, 56288], [-25, -5], [17, 204], [8, -199]], [[15644, 56388], [-8, -119], [-37, 104], [7, 218], [38, -203]], [[15681, 56759], [32, -85], [-33, -118], [-44, 77], [45, 126]], [[17346, 51603], [-324, -2], [-267, -1], [-25, -98], [-134, -39], [-24, -106], [-76, -65], [-82, -192], [-68, -51], [-41, 39], [-78, -170], [-77, -20], [-28, 148], [-83, 39], [-62, -15], [-48, -151], [-81, -144], [-133, 183], [-19, 600], [-30, 248], [-56, 150], [-18, -15]], [[15592, 51941], [-33, -53], [-51, 238], [-34, -30], [-26, 86], [-49, -124], [-53, 221], [0, 569], [12, -165], [1, -349], [16, -14], [18, 293], [-2, 411], [-20, -89], [-34, 81], [-11, 272], [88, 138], [-92, 126], [-30, 527], [-17, 107], [-9, 361], [-20, 370], [-60, 317], [-27, 500], [20, 283], [-18, 131], [42, -33], [49, -147], [48, -67], [49, -155], [98, -22], [77, -91], [49, -5], [26, 118], [44, -263], [38, 164], [65, -448], [-48, -214], [-62, -404], [-40, -396], [60, 456], [48, 122], [40, 341], [19, -10], [-13, 212], [22, -76], [11, -325], [-28, -82], [0, -182], [23, -145], [-21, -351], [4, -137], [-43, 142], [-14, -315], [-3, 337], [-37, -138], [-31, -297], [60, 84], [25, -146], [43, 229], [10, 203], [24, -91], [31, 149], [-20, 417], [20, 63], [-25, 118], [30, 535], [29, 86], [-34, 196], [-28, 275], [-20, -166], [-6, 196], [40, 103], [-54, 254], [-18, -64], [-16, 158], [20, 57], [43, -100], [-13, 226], [23, 40], [-42, 325], [-14, -100], [-44, 458], [13, 58], [519, 0], [244, 0], [429, 0], [401, 0]], [[19530, 34460], [-1, -1012], [0, -9793]], [[19529, 23655], [-208, 0], [-337, 0], [-17, 13], [-465, 992], [-228, 481], [-354, 748], [28, 406]], [[17948, 26295], [11, 67], [41, -19], [22, 263], [-3, 184], [-19, 118], [-45, 104], [7, 220], [-14, 192], [2, 211], [25, 39], [25, 238], [13, 311], [-9, 411], [39, 348], [44, 142], [28, 247], [-67, 264], [-1, 121], [-36, 425], [-33, 240], [-4, 237]], [[17974, 30658], [-2, 254], [18, 102], [-6, 309], [-21, 313], [7, 216], [-13, 169], [6, 315], [-20, 191], [6, 214], [48, 119], [56, -39], [16, -156], [46, -22], [23, 313], [0, 1510]], [[18138, 34466], [348, -2], [217, -1], [435, -2], [392, -1]], [[16869, 26908], [60, -357], [-33, 6], [-27, 351]], [[16886, 27750], [45, -128], [19, -192], [-44, 27], [-20, 293]], [[16466, 28799], [12, -165], [-31, -91], [-38, 205], [57, 51]], [[16524, 28868], [77, -132], [-45, -68], [-37, 16], [-18, 213], [23, -29]], [[17948, 26295], [-421, -218], [-249, -127], [-1, 301], [-36, 22], [2, 350], [-19, 426], [-52, 430], [-76, 371], [-55, 203], [-31, 181], [-46, 66], [-10, -119], [-34, 72], [6, 181], [-26, 310], [-17, 77], [-57, -15], [-17, -62], [-113, 279], [-15, 209], [-81, 304], [-88, -16], [-71, 127], [-95, -44], [-11, 135], [-38, 124], [12, 227], [-3, 291], [-17, 95], [9, 441], [-34, 39], [-37, 172], [19, 164], [-22, 213], [-26, 24], [-46, 336], [-33, 53], [-14, 232], [-35, 191], [-8, 182], [-21, 72], [-39, 309], [-54, 232], [-12, 345], [1, 293], [29, -8], [13, 325], [-19, 232], [-23, 96], [-29, -58], [-31, 55], [-68, 390], [0, 340], [-34, 445], [2, 358], [29, 55], [9, -392], [36, -69], [42, -215], [19, 15], [-32, 221], [-5, 162], [-47, 225], [1, 238], [-17, 0], [6, 200], [39, 88], [17, -58], [74, 48], [-43, 152], [-22, -173], [-38, 61], [-36, 150], [-28, -75], [-1, -360], [18, -81], [-26, -126], [-48, 145], [-37, 218], [-49, -11], [18, 273], [-7, 232], [-24, 57], [-19, 292], [-58, 224], [-21, 190], [-87, 479], [10, 209], [-39, 608], [17, 376], [-25, 550], [-72, 522], [-70, 294], [-12, 347], [25, 352], [26, 109], [1, 135], [31, 492], [-14, 236], [11, 81], [17, 483], [-27, 574], [-30, 76], [16, 218], [-1, 207]], [[15304, 43982], [372, 0], [437, 0], [365, 0]], [[16478, 43982], [0, -5714], [130, -623], [261, -1247], [131, -621], [153, -788], [255, -1312], [224, -1201], [185, -991], [157, -827]], [[21478, 40174], [4, -5714]], [[21482, 34460], [-268, 0]], [[21214, 34460], [-421, 0], [-263, 0], [-264, 0], [-473, 0], [-263, 0]], [[19530, 34460], [0, 7617]], [[19530, 42077], [481, 0], [262, 0], [349, 0], [299, 0]], [[20921, 42077], [274, -1], [283, 0], [0, -1902]], [[16478, 43982], [260, 0], [363, 0], [208, 0]], [[18138, 43983], [0, -9517]], [[21214, 33508], [-12, -1], [0, -1153], [-6, -7413], [-248, -1], [-434, -1], [-320, -1], [16, -313], [27, -125]], [[20237, 24500], [-476, 14], [0, -858], [-232, -1]], [[21214, 34460], [0, -952]], [[15304, 43982], [-41, 216], [-15, 259], [-7, 362], [11, 243], [-4, 182], [-28, 165], [-10, 184], [31, 400], [14, 395], [42, 540], [24, 638], [28, 1430], [-4, 364], [13, 177], [16, 690], [-12, 111], [24, 154], [6, 271], [-17, 30], [5, 288], [-12, 77], [12, 598], [-23, 284], [62, -244], [-16, 165], [36, -36], [68, 114], [28, -160], [41, -17], [16, 79]], [[18971, 43982], [0, -1905], [210, 0], [349, 0]], [[20921, 45888], [0, -3811]], [[20920, 49696], [1, -3808]], [[24913, 32555], [14, -197], [-32, -37], [18, -83], [-60, -186], [6, -357], [-23, -50], [-32, -290], [8, -421], [-25, -37], [-36, -246]], [[24751, 30651], [9, -148], [-53, -181], [-4, -171], [-21, 4], [-11, -185], [0, -414], [-47, -85], [-54, -479], [13, -178], [-49, -74], [13, -127], [-2, -221], [-52, -236], [22, -108], [-23, -127], [30, -170], [-15, -300], [19, -77], [6, -213], [-20, -93], [-1, -199]], [[24511, 26869], [-253, -3], [-299, -2], [-249, -1]], [[23710, 26863], [-2, 1033], [-40, 76], [-38, -67], [-43, 170]], [[23587, 28075], [12, 3311], [-50, 2121]], [[23549, 33507], [459, 0], [305, 0], [478, 0], [28, -260], [-23, -289], [-41, -174], [-23, -256], [181, 27]], [[24657, 44957], [50, -258], [17, -250], [65, -245], [8, -163], [-13, -373], [-37, -203], [-7, -235], [-27, -117], [-45, -63], [-28, -113], [-80, -34], [-24, -171], [-2, -293], [37, -217], [-4, -246], [-36, -235], [-16, -287], [-64, -141], [0, -392], [-17, -25]], [[24434, 40896], [-67, 327], [-13, 129], [-60, -25], [-320, -46], [-130, 8], [-256, -24], [-359, 52]], [[23229, 41317], [-21, 167], [4, 202], [-9, 593], [-27, 444], [8, 239], [-42, 162], [-6, 267], [11, 203], [-20, 132], [-6, 233], [-29, 134], [-25, 281], [4, 148], [-19, 163], [2, 243], [-24, 42]], [[23030, 44970], [-11, 248], [-32, 220], [20, 153], [31, 528], [-6, 174], [-27, 28], [10, 301], [-16, 215], [39, 3]], [[24488, 46841], [9, -224], [37, -196], [-23, -228], [7, -443], [29, -379], [93, -140], [17, -274]], [[23549, 34460], [-321, 0], [-322, 0], [-194, 0], [-321, 0], [-387, 0], [-257, 0], [-265, 0]], [[21478, 40174], [242, 0], [408, 0], [291, 0], [352, 0], [233, 0], [351, 0]], [[23355, 40174], [32, -167], [37, -90], [30, 79], [24, -314], [-24, 0], [-39, -403], [53, -311], [22, -285], [61, -123], [-2, -695], [0, -3405]], [[24434, 40896], [-22, -389], [21, -602], [26, -282], [74, -367], [10, -125], [65, -253], [20, -148], [12, -470], [21, -201], [24, 8], [31, 140], [69, -161], [16, -101], [-20, -164], [0, -201], [-46, -513], [5, -308], [65, -363], [53, -122], [56, -220], [20, -198], [28, -67], [6, -259], [21, -232], [-20, -254], [36, -465], [33, -118], [4, 157], [38, -198]], [[25080, 34420], [-2, -415], [-26, -344], [-37, 110], [-19, -265]], [[24996, 33506], [-20, 4]], [[24976, 33510], [-17, -2]], [[24959, 33508], [8, -222], [-27, -81], [12, -165], [-31, -14], [23, -202], [-31, -269]], [[23549, 33507], [0, 953]], [[23355, 40174], [-26, 63], [2, 154], [-63, 440], [-19, 330], [-20, 156]], [[20921, 45888], [339, 0], [237, 0], [427, -2], [238, 0], [310, 0], [113, -375], [35, -35], [32, 166], [50, -34], [100, 64], [48, -186], [76, -94], [41, -120], [27, -273], [36, -29]], [[23587, 28075], [-63, 84], [-46, 122], [-28, 197], [-68, 198], [-38, -181], [-54, 53], [-14, 107], [-48, -172], [-46, 65], [-98, -253], [-11, -116], [-23, 160], [-24, 0], [-43, 246], [-11, -116], [-45, 33], [-17, 161], [-45, -180], [-5, -228], [-21, -9], [-12, 329], [-45, -152], [-55, 131], [-9, 152], [-22, 9], [-33, -183], [-42, -9], [-2, 215], [-36, 60], [-1, 212], [-78, -2], [-34, -119], [-23, 146], [-40, -28], [-65, 143], [-67, 38], [0, 171], [-20, 171], [-32, 97], [-10, -157], [-37, 62], [-32, -43], [-61, 333], [-33, 20], [-1, 3665], [-350, 1], [-485, 0]], [[24334, 20156], [-61, 151], [29, 150], [55, -125], [-23, -176]], [[25053, 21267], [-35, -13], [42, 169], [-7, -156]], [[24968, 21496], [-51, -34], [-12, 119], [-60, 52], [-25, 182], [-46, 51], [-51, -318], [-8, -147], [15, -119], [59, -108], [37, 30], [42, 218], [24, -105], [44, 78], [-24, -221], [-30, 4], [4, -131], [29, 6], [15, -162], [17, 29], [10, 210], [24, 151], [24, -45], [-17, -153], [34, -184], [-20, -206], [-9, 85], [-46, -156], [18, -139], [-44, 72], [21, -136], [-38, 12], [24, -213], [41, -100], [-6, -117], [57, -34], [27, -142], [15, 58], [46, -378], [-35, -189], [-23, 97], [-49, -249], [31, 306], [-24, -79], [-32, 269], [-35, 107], [-54, 58], [19, 146], [-92, 276], [-36, 2], [39, -150], [9, -229], [-16, -106], [12, -104], [-22, -185], [-28, -87], [-6, 264], [-41, 165], [-50, -51], [-18, -243], [-18, -71], [-39, 119], [-10, -92], [-30, 143], [-25, -25], [-69, 188], [19, 138], [39, -55], [-40, 249], [9, 265], [-19, -237], [-65, 95], [-8, 208], [-22, -15], [-12, 225], [-49, -65], [3, 214], [-38, 1], [-38, -167], [-23, 38], [27, -274], [23, 14], [-70, -179], [-113, 134], [-96, 246], [-48, 76], [-83, -7], [-68, -62], [-25, -70], [-16, 203], [29, 95], [-4, 246]], [[23778, 21101], [22, 179], [1, 395], [-10, 201], [13, 169], [-7, 161], [31, 283], [22, 429], [-11, 130], [18, 65], [-46, 550], [7, 82], [-25, 188], [3, 94], [-28, 154], [6, 201], [-31, 366], [-33, 174], [0, 1941]], [[24511, 26869], [26, -173], [-23, -320], [16, -359], [-3, -163], [54, -270], [-34, -367], [-14, 4], [-1, -257], [-46, -252], [-22, -45], [-19, -228], [-9, -315], [-20, -83], [3, -214], [-12, -257], [-26, -7], [2, -260], [12, -163], [-21, -107], [264, 0], [272, 0], [-31, -605], [16, -248], [32, -195], [10, -281], [31, -208]], [[22840, 13727], [-7, 67], [-20, 607], [-38, 691], [-2, 561], [6, 120], [2, -489], [9, -333], [29, -592], [21, -632]], [[22836, 16657], [-39, -561], [-9, -234], [-13, -12], [8, 227], [31, 467], [56, 442], [-34, -329]], [[22878, 17074], [14, 264], [31, 240], [5, -124], [-50, -380]], [[23051, 17975], [-82, -302], [-37, -186], [7, 189], [24, 41], [81, 343], [7, -85]], [[23495, 19896], [25, -39], [-109, -464], [84, 503]], [[23778, 21101], [-16, 2], [-22, -401], [16, -192], [-63, -9], [-154, -416], [26, 233], [-54, -96], [18, 328], [-15, 164], [-23, -27], [-13, -195], [-50, 159], [19, -142], [-11, -252], [33, -88], [-16, -51], [21, -179], [-49, -326], [-24, -56], [-2, -246], [-58, -302], [-66, -235], [-97, -143], [0, -118], [65, 169], [-51, -186], [-35, 62], [-56, -120], [11, 197], [-44, -106], [-20, 180], [0, -164], [-38, 7], [-27, 157], [23, -294], [46, -268], [-70, -220], [-39, 306], [3, -453], [-45, -220], [7, 261], [-18, -256], [-10, 152], [-55, -255], [25, -112], [30, 160], [-1, -112], [-44, -390], [-19, 73], [-76, -3], [37, -52], [5, -174], [31, -143], [-46, -670], [-27, -95], [10, 233], [-33, -191], [-48, 321], [31, -300], [-13, -83], [53, -67], [23, 75], [-11, -475], [-20, -25], [-1, -350], [19, -95], [16, -491], [-5, -185], [37, -297], [1, -269], [28, -62], [-7, -204], [26, 121], [1, -155], [-32, -5], [-28, -97], [-3, -130], [-44, 130], [-38, 226], [-40, 62], [-118, 23], [-40, 225], [-59, 131], [-16, -39], [-45, 248], [-79, 68], [-8, 223], [-14, 29], [-24, 547], [-59, 437], [6, 358], [-23, 127], [14, 219], [-11, 281], [-25, 138], [-29, 24], [-44, 277], [-4, 185], [-28, 173], [-27, 308], [-56, 233], [-35, 620], [-26, 202], [-12, 243], [-24, 169], [-12, 365], [-29, 135], [-12, 160], [-58, 247], [-4, 112], [-53, 181], [-2, 130], [-22, -47], [-27, 318], [-31, 1], [-7, 104], [-24, -85], [-133, 51], [-59, 173], [-23, -221], [-31, 14], [-45, -76], [-28, -291], [-34, -601], [11, -91], [-36, -98], [-44, -366], [-43, 33], [-49, 152], [-22, 140], [-32, 33], [-34, 200], [-63, 81], [-40, 183], [-29, 210], [-23, 21], [-47, 222], [-14, 254], [-30, 284], [-7, 225], [6, 263], [-49, 427], [-8, 256], [-34, 238], [-62, 254], [-41, 94], [-50, 273], [-10, 131], [-49, 207], [-66, 413], [-57, 153], [-51, 511], [-32, 47]], [[29906, 44002], [2, -937], [-14, -395]], [[29894, 42670], [-15, 67], [-108, -120], [-17, 46], [-45, -97], [-27, 52], [-80, -73], [-5, 114], [-62, -302], [-14, 52], [-128, -330]], [[29393, 42079], [-22, 196], [66, 218], [-16, 147], [17, 1446]], [[30201, 46046], [10, -35], [-30, -358]], [[29721, 45371], [-22, 178], [18, 317], [9, 421], [13, 172], [1, 440], [26, 358], [25, 124], [45, 610], [3, 337], [17, 110], [39, 33], [46, 169], [36, 268], [-24, 294], [36, 341], [0, 179]], [[29989, 49722], [27, 364], [32, 167], [42, -77], [16, 100]], [[30106, 50276], [35, -3261], [-4, -398], [45, -317], [19, -254]], [[30063, 43031], [-30, -87], [35, 392], [-5, -305]], [[30096, 43055], [-23, -20], [3, 332]], [[30063, 43429], [-6, -111], [-36, 319], [1, -275], [-16, -190], [6, -168], [-21, -228], [-97, -106]], [[29498, 45403], [6, 1535], [-14, 129], [-31, -94], [11, 337], [-20, 595], [32, 346], [9, 424], [-22, 226], [10, 393], [-12, 117], [7, 294]], [[29474, 49705], [265, 9], [250, 8]], [[25368, 21594], [-61, -15], [56, 75], [5, -60]], [[26060, 30622], [82, -2762], [31, -1141], [33, -611], [32, -345], [-7, -184], [27, -130], [-34, -162], [-12, -349], [-23, -336], [28, -524], [-16, -698], [23, -182], [3, -165]], [[26227, 23033], [-310, 0], [-411, -1], [-9, -241], [32, -268], [27, -99], [-1, -382]], [[25555, 22042], [8, -57], [-56, -332], [-54, -74], [6, 82], [-37, 226], [-12, 193], [8, 160], [-27, 272], [-22, -304], [-9, -435], [-51, 119], [-29, -26]], [[25280, 21866], [-22, 2861], [30, 1543], [81, 4206], [-26, 171]], [[25343, 30647], [-1, 22], [406, -37], [312, -10]], [[27521, 11909], [-60, -399], [62, 529], [-2, 123], [31, 100], [-31, -353]], [[27042, 14480], [-27, 365], [18, -89], [9, -276]], [[27576, 15790], [-35, 409], [-13, 371], [48, -780]], [[27452, 18403], [-13, -250], [-2, -353], [-14, 224], [-5, 333], [34, 46]], [[26205, 20530], [-6, -100], [-29, 106], [35, -6]], [[25779, 21913], [15, -22], [-189, -141], [97, 127], [77, 36]], [[27204, 22485], [20, -58], [-7, -281], [11, -131], [5, -318], [61, -1358], [68, -923], [102, -1034], [11, -163], [-18, -126], [0, -378], [17, -360], [-25, 359], [6, 749], [-50, 50], [8, 219], [-27, 93], [5, -246], [25, -548], [100, -1348], [10, -288], [41, -607], [34, -425], [10, -308], [2, -426], [-22, -988], [-5, -560], [-3, 240], [-16, -279], [-30, -259], [-11, -272], [9, -198], [-38, -244], [-37, -3], [-36, -185], [-40, 80], [-15, -80], [-50, -45], [-25, 204], [8, 197], [57, -227], [-10, 202], [-45, 126], [-38, 609], [-22, 93], [-4, 158], [-69, 190], [-24, -55], [-32, 502], [-8, 502], [-45, 95], [64, 439], [-22, -50], [-25, -266], [-25, -39], [-12, 333], [8, 264], [-13, 104], [23, 145], [-38, -85], [-19, 72], [20, -309], [-46, 88], [-57, 697], [-15, 341], [-37, 180], [32, 93], [13, 214], [41, 278], [-7, 231], [-29, -176], [-20, 301], [-33, -106], [31, -87], [-9, -344], [-13, 0], [-42, 352], [17, 402], [-5, 120], [36, 548], [9, 810], [-27, 164], [-20, 411], [-67, 10], [-12, 174], [-47, 325], [-42, 171], [-2, 258], [-33, 93], [-42, 391], [-96, 352], [-43, -64], [-23, 50], [-36, -143], [11, -232], [-44, 38], [-66, -259], [-39, -107], [2, 142], [-35, -168], [-34, 10], [-61, -83], [12, 229], [-15, 173], [-88, 446], [52, -124], [-3, 156], [-24, -30], [-39, 136], [-9, 235], [-28, -103], [24, -46], [7, -193], [-90, 317], [-83, 143], [12, 105], [60, -69], [-28, 200], [-80, -62], [-52, -95], [-102, -63], [34, 154], [-25, 78], [-15, -97], [-31, 237], [6, -211], [-48, -294], [-33, -13], [29, 219], [-21, 99]], [[26227, 23033], [36, -536], [302, -118], [433, -175], [13, -364], [30, -21], [21, 429], [-8, 300], [21, 139], [71, -147], [58, -55]], [[27217, 22527], [-8, 324], [18, 40], [-10, -364]], [[27271, 23771], [2, 223], [22, 32], [-24, -255]], [[27374, 24999], [15, -57], [-40, -261], [-45, -20], [28, -53], [-18, -161], [-25, -48], [-29, -279], [-18, -296], [11, -127], [-40, -2], [55, -139], [-32, -253], [-20, 93], [9, -316], [-26, -348], [5, -247]], [[26060, 30622], [357, 3]], [[26417, 30625], [337, 27]], [[26754, 30652], [-54, -344], [-13, -212], [95, -433], [44, -39], [27, -321], [7, -205], [36, -245], [13, -189], [87, -393], [15, -220], [37, -115], [34, -187], [2, -219], [48, -391], [70, -254], [2, -174], [18, -158], [7, -427], [59, -267], [19, -373], [-7, -180], [22, -203], [52, -104]], [[25280, 21866], [-18, -121], [-33, 136], [-42, -78], [-29, 130], [-111, -194], [-22, 115], [-2, -149], [-26, -87], [-6, -128], [-23, 6]], [[24751, 30651], [362, -3], [230, -1]], [[28020, 28522], [0, -65], [-53, -132], [-49, -261], [-54, -427], [-14, -336], [-33, 106], [33, -215], [-25, -52], [-32, -266], [-29, -49], [-8, 95], [-29, -163], [10, -79], [-44, -218], [-29, -79], [-25, 183], [-1, -175], [18, -60], [-35, -228], [-56, -96], [-35, -141], [-56, 62], [15, -93], [-31, -26], [44, -90], [-11, -166], [-46, -106], [-34, 180], [-7, 249], [-20, 91], [16, -419], [33, -200], [-59, -347]], [[26754, 30652], [123, 241], [108, 150], [338, -99], [13, -176], [28, 89], [37, -279], [5, -259], [299, -38], [14, -19], [209, -1212], [92, -528]], [[25667, 44928], [-14, -294], [-39, -1109]], [[25614, 43525], [-87, 0], [-3, -4219], [-1, -348], [-25, -118], [7, -180], [-19, -167], [35, -281], [8, -394], [-42, -366], [5, -97], [-33, -113], [-25, -327], [-26, 19], [-13, -170], [17, -95], [-27, -118], [-2, -367], [-21, -13], [19, -175]], [[25381, 35996], [-33, -317], [22, -229], [-11, -86], [-91, -113], [-18, -213], [22, -257], [-14, -183], [-117, 281], [-40, -23], [-36, -277], [15, -159]], [[24657, 44957], [495, -24], [297, -14], [218, 9]], [[25614, 43525], [391, -1], [278, -2], [0, -118]], [[26283, 43404], [2, -767], [-7, -4182]], [[26278, 38455], [-15, -340], [26, -50], [-6, -186], [-54, -46], [-52, -147], [-20, 89], [-55, -27], [8, -359], [-54, -202], [-12, -234], [-42, -38], [-24, -208], [-4, -221], [-38, -179], [-65, 218], [1, 110], [-31, -47], [-36, -188], [1, -124], [-62, -96], [-22, 174], [-56, -143], [-27, -250], [-13, 111], [-73, 190], [-34, -62], [-30, -168], [-8, 154], [-32, -55], [-35, 87], [7, -178], [-40, -44]], [[26901, 37163], [5, -327], [-19, -164], [46, -332], [-3, -87], [47, -370], [5, -112], [51, -238], [42, -47]], [[27075, 35486], [-108, -496], [-78, -218], [-24, -229], [-37, -122], [-5, -150], [-60, -127], [-15, -161], [-104, -154], [-44, -124]], [[26600, 33705], [-17, -34], [-388, 75], [-85, -12], [-178, 29], [-135, 44], [-357, -25], [-65, 86], [6, -363], [-385, 1]], [[26278, 38455], [26, 92], [26, -131], [54, 47], [34, -138], [35, -417], [92, -92], [54, -251], [38, 147], [47, -92], [17, -111], [43, 34], [34, 184], [39, 55], [18, -301], [35, -97], [31, -221]], [[28864, 31094], [-45, -70], [52, 180], [-7, -110]], [[28770, 33602], [6, -1]], [[28776, 33601], [20, -509], [20, -289], [42, -447], [-44, 312], [-44, 934]], [[28746, 33603], [6, -1]], [[28752, 33602], [10, 0]], [[28762, 33602], [-29, 1]], [[28733, 33603], [13, 0]], [[28728, 33603], [-2, -94], [44, -381], [26, -418], [-10, 2], [-31, 391], [7, -233], [-31, 50], [-51, 210], [34, -237], [-32, -140], [-67, 117], [35, -131], [-53, -148], [-52, 70], [-18, 302], [-1, -186], [19, -209], [-12, -117], [48, -14], [24, 66], [46, -29], [31, 68], [40, -18], [-9, -229], [15, -405], [11, 450], [23, 97], [33, -10], [19, -232], [-1, -214], [-16, -233], [-32, 6], [-36, -305], [-34, -137], [-33, 2], [-38, 212], [-11, -106], [-37, 178], [32, 84], [-34, 16], [-5, -295], [-13, 82], [-31, -37], [-65, 150], [83, -273], [54, -81], [2, -155], [-51, -113], [32, -89], [-56, -257], [-21, 7], [-67, 312], [39, -337], [49, -128], [34, 135], [28, -97], [24, 268], [10, -166], [26, -19], [-3, -146], [-64, -306], [-14, 148], [-24, -174], [-54, 27], [-55, -118], [-8, 59], [-37, -217], [-41, -10], [18, 116], [-27, 196], [14, -230], [-18, -45], [19, -155], [-43, -111], [-37, -177], [-46, -322], [-28, -491], [3, 304], [-14, -297], [-17, -115], [-61, 63], [-89, -82]], [[26417, 30625], [7, 437], [18, 98], [38, -19], [24, 318], [67, 251], [76, 27], [71, 279], [47, 139], [26, -3], [21, 301], [29, -20], [9, 122], [39, 109], [9, -145], [32, 33], [34, 219], [46, 86], [31, -110], [45, 369], [50, 189], [24, 411]], [[27160, 33716], [5, -29], [115, -47], [153, -14], [164, -43], [395, 4], [216, 0], [520, 16]], [[27477, 41405], [-41, -72], [18, -234], [-3, -380], [-35, -430], [-8, -269], [-20, -245], [-10, -308], [-51, -189], [-42, -236], [-50, -83], [-25, 73], [-28, -239], [-29, -8], [-17, -148], [-5, -488], [-20, 23], [-19, -142], [-10, 221], [-25, 60], [-18, -102], [-33, -392], [10, -268], [-24, -47], [-24, -303], [-67, -36]], [[26283, 43404], [140, 21], [248, 53], [79, 387]], [[26750, 43865], [17, -172], [112, -342], [68, 16], [319, 1013], [211, 219]], [[27477, 44599], [0, -3194]], [[26600, 33705], [477, -13], [83, 24]], [[24976, 33510], [-17, -2]], [[28253, 38900], [49, -62], [26, -105], [-12, -203], [24, -120], [42, -50], [43, -217]], [[28425, 38143], [14, -61]], [[28439, 38082], [6, -355], [-51, -113], [-34, -469], [29, -152], [58, 125], [22, -338], [22, -78], [74, -51], [24, -239], [78, -232], [-18, -87], [8, -279], [-10, -166], [-41, 162], [-13, -76], [-19, 206], [-69, 316], [-25, 208], [-1, -136], [36, -169], [22, -199], [29, -92], [17, -214], [34, -5], [40, -104], [-30, -52], [41, -177], [-3, -217], [-18, 96], [-34, -26], [18, -190], [-34, -45], [-78, 546], [49, -458], [38, -154], [18, 37], [31, -197], [3, -194], [-41, -100], [-7, 103], [-46, 192], [-10, 196], [-21, -26], [-72, 191], [-19, -63], [-54, 119], [21, -117], [44, 12], [12, -105], [85, -105], [3, -269], [46, -171], [5, -86], [44, -47], [14, 149], [78, -95], [36, -691]], [[28770, 33602], [-22, 323], [-2, -322]], [[28733, 33603], [-5, 0]], [[27075, 35486], [-6, -128], [45, -353], [50, -147], [27, 6], [51, 233], [39, -172], [77, 96], [19, 205], [40, -37], [65, 166], [8, -75], [50, 150], [19, 201], [-18, 114], [23, 266], [58, 357], [22, 329], [36, 200], [44, 595], [23, -47], [12, -167], [59, -105], [30, 209], [12, 196], [42, 398], [32, -113], [38, 273], [20, -9], [72, 451], [1, 203], [25, 347], [135, -584], [28, 356]], [[24848, 54050], [-127, -1338], [51, -142], [31, -321], [278, -367], [79, -215], [60, -54], [26, 36], [113, -179], [6, -254], [56, -75], [28, -148], [-1, -345], [-16, -271], [46, 57], [12, -69], [-23, -304], [23, -135], [60, -80], [9, 240], [25, 72], [62, 383], [92, 0], [143, -395], [-56, -231], [-74, -515], [-49, -720], [-30, -569], [-32, -767], [-12, -540], [7, -466], [32, -1410]], [[27768, 39644], [-3, -976], [97, 395], [21, 125], [25, -61], [57, 355], [5, -111], [48, -94], [31, 8], [9, 173], [43, 21], [40, 125], [47, -189], [34, 18], [-4, -182], [21, -45], [14, -306]], [[27477, 41405], [0, -1762], [291, 1]], [[28897, 39805], [-47, -299], [11, -206], [-10, -150], [43, -281], [11, -172], [0, -322], [23, -243], [41, -255], [23, 3], [11, -652]], [[29003, 37228], [-4, -1]], [[28999, 37227], [-7, 0]], [[28992, 37227], [-170, 14], [-27, 2261], [0, 141]], [[28795, 39643], [52, 222], [50, -60]], [[28452, 37892], [-13, 190]], [[28425, 38143], [20, 119], [38, -197], [-31, -173]], [[28992, 37227], [-3, -224], [-17, -180], [-22, -3], [-56, -529], [-10, -210], [-97, -756], [-23, -246], [-10, -362], [-18, 113], [-2, 232], [24, 561], [29, 117], [12, 180], [23, 68], [17, 299], [-43, 32], [-20, -87], [4, 292], [18, 136], [-38, -6], [27, 156], [-24, 71], [17, 254], [-31, -280], [-20, -30], [-69, 430], [27, 215], [-30, -34], [2, 118], [74, -112], [-52, 208], [-10, 157], [-21, 7], [14, 149], [24, -158], [-4, 172], [23, 60], [-39, 139], [-23, -67], [4, 216], [27, -106], [42, 327], [-30, -41], [-20, -174], [-7, 258], [46, 389], [20, 39], [28, 403], [-42, -89], [5, -165], [-40, -202], [-12, 170], [-9, -273], [-7, 175], [-24, -315], [-47, 52], [41, -334], [-23, -20], [29, -123], [-39, -234], [12, -125], [-8, -192], [6, -436], [33, -212], [-10, -120], [-61, 291], [70, -471], [18, -169], [-1, -180], [-66, 295], [-77, 111], [-38, 166], [-20, 195], [-44, -157], [-15, 279], [21, 180], [41, 196], [4, 179]], [[27768, 39644], [225, -1], [321, 0], [481, 0]], [[29009, 40204], [82, 279], [-69, 480], [-25, 74], [-7, 200], [-26, 23], [-4, 248], [38, 363], [-22, 173], [32, 123], [39, 255], [11, 176], [41, 159]], [[29099, 42757], [221, -696]], [[29320, 42061], [-35, -528], [-50, -152], [-20, -260], [7, -71], [74, -110], [5, -142], [-21, -520], [-21, -140], [-11, -528], [-54, -293], [-31, -287], [15, -81], [-32, -88], [-35, -208], [-25, -280], [-36, -211], [-26, -15], [23, 401], [-43, 130], [-25, -52], [-34, 209], [-30, 78], [-48, 266], [-7, 273], [41, 346], [74, 136], [34, 270]], [[29273, 41381], [-8, -151], [-39, -105], [20, 274], [27, -18]], [[29782, 42336], [-46, -134], [6, -65], [-65, -235], [47, 4], [25, 162], [72, 12], [45, 138], [25, -3], [-153, -400], [-68, -124], [-22, 21], [-42, -118], [-37, 7], [-41, -95], [-182, -221], [-49, -28], [-8, 207], [30, 212], [36, 6], [17, 141], [65, 150], [72, -70], [21, 104], [103, 5], [49, 47], [73, 287], [27, -10]], [[29393, 42079], [-34, -173], [-9, -184], [-36, -16], [6, 355]], [[29099, 42757], [-10, 128], [-42, 47], [-31, 125], [-19, 194], [-4, 404], [-48, 104], [-24, 219], [-465, 1], [-265, 1], [-265, 1], [-238, 1], [0, 1025]], [[27688, 45007], [207, 504], [29, 276], [-32, 126], [-6, 470], [-34, 389], [136, 317], [514, -11], [12, 38], [96, 836], [35, 184], [40, 85], [33, 233], [55, 116], [47, 329], [120, 538], [112, 278], [48, -23], [374, 13]], [[29009, 40204], [-31, -197], [-72, -139], [-9, -63]], [[27477, 44599], [76, 80], [135, 328]], [[30741, 47862], [-27, 147], [36, -61], [-9, -86]], [[30794, 48218], [-29, -99], [14, 186], [15, -87]], [[30910, 48379], [-34, -45], [-1, -112], [-31, 140], [23, 236], [22, 30], [21, -249]], [[30106, 50276], [37, 52], [13, -163], [31, 351], [40, -7], [-23, 123], [40, 305], [51, 152], [-3, 138], [37, 164], [-8, 361], [35, 531], [35, 166], [15, 493], [206, 1401], [59, -41], [5, -324], [37, -112], [73, 106], [82, 216], [43, -33], [108, -508], [6, -2122], [-4, -496], [39, -119], [59, -51], [-12, -224], [18, -202], [-12, -230], [41, -214], [41, 47], [38, -391], [-22, -47], [39, -248], [-61, -332], [-36, 25], [-50, -91], [-18, -134], [-47, 93], [-38, -88], [-7, -275], [-24, 132], [1, -159], [-22, -105], [-14, 290], [-89, 12], [-40, -145], [10, -330], [-74, 252], [18, 320], [-17, 43], [-12, -202], [-39, -62], [11, -198], [-15, -43], [-25, -301], [4, -160], [-53, -234], [-29, 81], [1, 168], [-41, -431], [-13, 248], [-22, -229], [-5, 313], [-29, -233], [15, -113], [-14, -128], [-17, 526], [8, -577], [-24, 155], [-33, -169], [11, 235], [-43, -81], [-50, -311], [23, -156], [-39, -41], [3, -176], [-69, -328], [-22, -345], [-23, -20]], [[24976, 55400], [44, -21], [194, 519], [83, 80], [365, -983], [601, -1684], [29, -504], [51, -302], [28, -34], [19, 104], [87, 27], [-14, -176], [20, -442], [39, -269], [30, 112], [63, -6], [41, -234], [-36, -336], [281, -848], [16, -148], [26, -801], [80, -2403], [-1, -117], [-72, -950], [-20, -443], [0, -189], [-18, -245], [-27, -62], [-43, -267], [-67, -164], [-25, -174], [-8, -369], [8, -206]], [[99717, 62342], [61, -118], [81, -342], [-56, 105], [-38, 184], [-57, 66], [9, 105]], [[777, 62716], [26, -103], [-38, -77], [12, 180]], [[362, 62745], [54, -60], [-43, -46], [-25, -367], [-58, 118], [40, 187], [-71, 215], [72, 91], [31, -138]], [[560, 62515], [-40, -97], [-30, 89], [-89, -40], [114, 147], [34, 291], [32, -50], [-23, -166], [2, -174]], [[720, 62952], [-23, -196], [60, -44], [2, -151], [-48, -132], [-34, 0], [-25, -143], [-7, 207], [-28, -234], [-11, 139], [22, 78], [-8, 154], [52, -18], [6, 148], [-21, 146], [63, 46]], [[99653, 62831], [-22, 91], [40, 16], [-18, -107]], [[99968, 62794], [-48, 74], [-3, 110], [49, 104], [33, -118], [-31, -170]], [[876, 63090], [-12, -132], [-42, 183], [34, 92], [20, -143]], [[99385, 63007], [-3, -147], [-56, 0], [-12, -162], [-27, 124], [45, 165], [35, 43], [23, 235], [27, -66], [-32, -192]], [[1546, 63317], [66, -118], [62, 39], [49, -39], [-97, -50], [-49, -75], [-99, 36], [-62, 154], [130, 53]], [[1882, 63591], [-68, -66], [53, 262], [38, -104], [-23, -92]], [[1399, 63788], [34, -203], [-57, -153], [28, -189], [-91, -7], [-79, -177], [-48, 50], [-41, -73], [-81, 6], [45, 78], [40, -32], [31, 158], [34, -72], [51, 53], [12, 142], [60, 13], [31, 155], [-57, 105], [68, 182], [20, -36]], [[98330, 63982], [-22, -96], [9, -170], [-95, 67], [60, 194], [48, 5]], [[2381, 64192], [-56, -135], [-14, 84], [28, 179], [49, 0], [-7, -128]], [[2607, 64726], [30, -65], [-17, -151], [-76, 113], [63, 103]], [[98083, 64934], [62, -11], [55, -151], [-17, -84], [50, -27], [-34, -89], [-64, 37], [-41, -153], [-50, 87], [13, 155], [-51, -10], [3, 107], [-45, -2], [51, 155], [68, -14]], [[3101, 66018], [56, -95], [-11, -257], [-80, -213], [-42, -58], [-57, -383], [-26, -22], [-54, -222], [-60, -80], [63, 398], [3, 202], [43, 168], [48, -51], [19, 418], [46, 138], [52, 57]], [[3637, 66541], [-25, -202], [-34, 82], [59, 120]], [[3453, 66789], [23, 74], [16, -348], [41, 104], [16, 197], [52, -105], [-14, -114], [-46, -82], [-40, -222], [63, 130], [22, -182], [-82, -132], [-8, -156], [-60, 39], [16, -201], [-34, 58], [-144, -261], [-32, -161], [-45, -34], [-51, 129], [37, 136], [68, 101], [43, -34], [8, 114], [37, 2], [0, 277], [46, -9], [-20, 123], [42, 63], [32, -160], [-3, 187], [-63, 49], [-36, 194], [39, 198], [77, 26]], [[3958, 67075], [-11, -94], [-70, 29], [25, 79], [56, -14]], [[3697, 67169], [60, -94], [-65, -169], [-41, 0], [-24, 156], [42, 193], [28, -86]], [[3796, 67351], [21, -133], [-41, -170], [-27, 236], [16, 117], [31, -50]], [[4603, 67703], [20, -131], [-70, 71], [-4, 154], [54, -94]], [[4698, 68459], [-41, 165], [52, 56], [-11, -221]], [[13345, 68693], [12, -141], [-37, -75], [-40, 159], [65, 57]], [[4286, 68856], [51, -6], [36, -197], [20, -307], [65, -42], [26, -202], [-54, 36], [-36, 155], [-16, -161], [-55, -130], [-46, 50], [-95, -28], [-56, -169], [-20, -188], [-70, -84], [-40, 23], [-31, 136], [-13, 209], [72, 178], [43, 398], [40, 77], [48, -63], [20, 118], [111, 197]], [[4851, 69047], [25, -78], [-31, -117], [-43, 146], [49, 49]], [[12913, 69071], [35, -233], [-23, -106], [-34, 78], [22, 261]], [[5465, 68969], [0, -138], [-37, 34], [5, 131], [32, -27]], [[12839, 69091], [33, -448], [50, -236], [26, -280], [-48, 31], [0, 92], [-81, 383], [-24, 297], [10, 260], [34, -99]], [[13314, 68975], [-6, -200], [-70, -1], [27, 489], [38, -112], [11, -176]], [[5354, 69164], [6, -156], [-78, -421], [26, 251], [-29, 135], [52, -4], [-17, 156], [40, 39]], [[12775, 69347], [23, -91], [-27, -128], [-39, 50], [2, 162], [41, 7]], [[5240, 69412], [0, -197], [-55, 145], [55, 52]], [[5141, 69327], [42, 108], [-11, -245], [11, -195], [-62, 106], [-17, -132], [-9, 404], [45, 143], [1, -189]], [[13184, 69529], [51, -218], [-29, -298], [-22, 105], [-12, 333], [12, 78]], [[12697, 69549], [35, -84], [-34, -115], [-11, -160], [-15, 261], [25, 98]], [[12718, 69559], [-29, 35], [26, 143], [3, -178]], [[12762, 69626], [-28, 121], [32, 55], [-4, -176]], [[12675, 69774], [-1, -180], [-42, 0], [43, 180]], [[12692, 70287], [77, -56], [-49, -144], [-39, 44], [11, 156]], [[6567, 70232], [-41, -15], [-7, 108], [53, 158], [-5, -251]], [[12508, 70495], [41, 0], [-44, -195], [-24, 176], [27, 19]], [[13354, 70541], [68, -465], [-3, -597], [-22, -217], [-37, -167], [-34, 95], [28, 166], [-20, 92], [-11, -198], [-35, 20], [49, 323], [-26, 373], [6, -430], [-51, -225], [-45, 105], [-34, 233], [45, 140], [-14, 126], [8, 425], [56, -14], [-30, 143], [83, 110], [19, -38]], [[12746, 70951], [25, -18], [0, -264], [-47, 10], [-64, -218], [-27, 77], [33, 239], [48, 17], [32, 157]], [[12999, 71016], [34, 45], [76, -213], [-24, -269], [-40, -96], [-30, 104], [12, 117], [-90, 165], [8, 213], [43, 217], [34, 14], [8, -150], [-31, -147]], [[12692, 71318], [82, -75], [31, 36], [43, -320], [-31, -86], [20, -128], [91, -106], [77, -418], [9, -256], [19, 42], [20, -243], [-62, 42], [-41, -159], [72, 78], [8, -258], [34, 62], [45, -261], [-40, -60], [34, -83], [38, 84], [-28, -375], [-41, -113], [24, -37], [48, 115], [3, -366], [-15, -275], [-40, 9], [-41, 183], [-30, 420], [-53, -89], [23, 275], [-35, 243], [3, -178], [-43, 79], [-5, 156], [-108, 12], [-8, 198], [96, -41], [-65, 196], [11, 230], [26, 109], [-77, -105], [-37, 86], [22, 255], [43, 104], [-25, 156], [-14, 509], [-80, 33], [-3, 320]], [[12956, 71488], [-3, -304], [-47, -89], [-61, 180], [18, 199], [93, 14]], [[13092, 71316], [35, 4], [28, -273], [-42, -174], [-77, 302], [-3, 413], [59, -272]], [[6152, 71755], [23, -87], [-92, -12], [14, 101], [55, -2]], [[6876, 71799], [-8, -169], [-69, -226], [11, 252], [66, 143]], [[6981, 71800], [9, -91], [-43, -104], [-36, 30], [16, 149], [54, 16]], [[2618, 71827], [76, -52], [-45, -92], [-31, 144]], [[12888, 72172], [19, -8], [78, -375], [-72, -197], [-40, 39], [15, 541]], [[12581, 72294], [29, -299], [6, 193], [40, -74], [-16, -137], [18, -181], [-42, -31], [-17, -141], [22, -313], [-18, -139], [-10, -330], [-27, 18], [1, 370], [-29, -501], [-27, 142], [-12, 466], [28, 149], [35, -140], [5, 206], [-76, 170], [23, 81], [-46, 212], [3, 282], [20, 59], [56, -98], [-43, 182], [77, -146]], [[12643, 72697], [125, -164], [58, 15], [48, -251], [-17, -112], [15, -346], [-19, -39], [-95, 431], [36, -353], [40, -222], [-20, -130], [-142, 4], [6, 270], [-17, 101], [5, 371], [-16, -86], [-28, 211], [-50, 187], [4, 87], [67, 26]], [[7232, 72884], [84, -48], [-91, -135], [-37, -143], [-19, 143], [42, 241], [21, -58]], [[2509, 72978], [-34, -159], [-42, 106], [76, 53]], [[12140, 73010], [7, -191], [-23, -249], [-60, -39], [5, 171], [32, 169], [-42, 104], [5, 194], [32, 8], [44, -167]], [[12184, 73405], [74, 39], [85, -403], [63, -1008], [-3, -889], [-9, -166], [-63, 295], [-48, 397], [29, 94], [-47, 143], [24, 215], [-43, -187], [19, 295], [-41, -82], [-29, 76], [-1, 216], [58, 190], [-40, 10], [-28, 211], [22, 176], [-56, -26], [-40, 215], [83, 389], [28, -81], [-37, -119]], [[7211, 74241], [-6, -131], [-73, 222], [17, 80], [62, -171]], [[7280, 74320], [57, -40], [-32, -267], [18, -53], [11, 229], [22, -64], [37, 206], [34, -46], [35, -213], [-31, -139], [13, -214], [77, 17], [-45, -216], [-8, -145], [-55, 17], [-59, 128], [-63, 17], [94, -214], [-8, -202], [-49, -55], [-11, 156], [-82, -91], [59, -91], [-37, -74], [-45, 35], [-68, -309], [-47, -122], [-19, -231], [-63, -268], [-45, -14], [17, 203], [63, 237], [-37, -17], [45, 364], [-82, -361], [-9, 75], [48, 221], [-110, 53], [-38, -65], [23, -121], [38, 128], [42, 12], [-9, -294], [-42, -128], [-62, 170], [-1, 349], [-19, 160], [-58, 167], [49, 319], [77, 256], [39, 52], [60, -87], [32, -423], [39, -199], [-28, 487], [39, -38], [-50, 208], [79, -20], [-89, 175], [0, 154], [55, 174], [42, -119], [7, -268], [36, 131], [-15, 136], [74, -102], [24, 211], [-44, 218], [75, -155]], [[11925, 74483], [-31, -320], [-26, 407], [20, 71], [37, -158]], [[7226, 74643], [88, -193], [-31, -26], [-117, 156], [34, 154], [26, -91]], [[12100, 74892], [63, -129], [-50, -365], [64, 208], [13, 117], [130, -216], [-12, -269], [-65, 120], [74, -260], [-91, -32], [-164, 374], [16, -111], [117, -245], [43, -187], [85, 93], [28, -492], [-68, -59], [-198, 558], [16, -212], [28, -79], [15, -292], [-43, -166], [-40, 53], [-45, 236], [50, -56], [-87, 297], [17, 84], [-92, 261], [29, 323], [71, -276], [-104, 488], [44, 0], [-23, 215], [26, -5], [27, -219], [8, 231], [42, -106], [48, 203], [28, -85]], [[12471, 74972], [34, -143], [-101, 95], [-11, 140], [50, 26], [28, -118]], [[12322, 75166], [29, -82], [32, -309], [141, 0], [6, -172], [77, -532], [9, -268], [-29, 66], [-74, 706], [-25, -127], [14, -207], [93, -513], [12, -245], [-49, -54], [49, -50], [8, -152], [-33, -89], [-55, 150], [29, -209], [-23, -144], [-56, -130], [2, -90], [-73, -145], [10, 275], [-12, 143], [46, 156], [-28, 164], [68, -125], [1, 118], [-44, 0], [-23, 141], [55, 145], [-60, -63], [-28, 209], [-9, 365], [-28, 424], [0, 148], [-31, 168], [-17, 309], [16, 19]], [[7340, 75002], [11, 139], [-35, 92], [69, 145], [73, -266], [3, 133], [48, -116], [21, 90], [13, -234], [18, 140], [9, -218], [-40, -171], [-57, 193], [-6, -271], [-57, -39], [-5, 209], [-23, -236], [-37, -17], [-8, -136], [-100, 227], [-6, 207], [78, -83], [-57, 182], [19, 65], [69, -35]], [[7428, 75370], [-48, 119], [79, 181], [2, -164], [-33, -136]], [[5104, 75701], [-12, -119], [-59, -86], [5, 293], [103, 225], [-37, -313]], [[7171, 77004], [-42, 9], [43, 124], [-1, -133]], [[9629, 77917], [66, 317], [37, 52], [-103, -369]], [[8689, 78510], [20, -42], [-68, -196], [48, 238]], [[8654, 78521], [-25, -185], [-28, 124], [53, 61]], [[8924, 78943], [1, -134], [51, 52], [-86, -344], [-71, -369], [6, -119], [-125, -160], [60, 390], [88, 271], [53, 212], [23, 201]], [[9479, 78882], [-46, 32], [52, 143], [-6, -175]], [[3632, 79010], [33, -136], [49, 49], [35, -88], [-12, -445], [48, -251], [-56, -18], [-111, -186], [-12, -125], [-57, 177], [-74, 59], [-178, 351], [-35, 239], [40, 82], [116, -53], [23, 155], [60, 44], [32, 137], [22, -73], [77, 82]], [[9083, 79156], [52, 5], [-3, -117], [60, 55], [-20, -166], [-31, 2], [-74, -204], [-23, 78], [53, 143], [-64, 47], [50, 157]], [[8763, 79223], [20, -127], [-34, -337], [-48, -41], [42, 196], [-28, 68], [48, 241]], [[7552, 78931], [23, 301], [25, -21], [-48, -280]], [[1842, 79009], [40, -83], [-51, -39], [-131, 340], [32, 192], [5, -149], [105, -261]], [[9206, 79177], [-54, -47], [2, 119], [146, 133], [-94, -205]], [[8668, 80036], [32, -200], [-56, -53], [24, 253]], [[4669, 85164], [-1, -116], [-93, 25], [37, 101], [57, -10]], [[2081, 85494], [16, -196], [188, -232], [82, 216], [97, 20], [60, -153], [12, -159], [71, -184], [53, -20], [12, -117], [232, -98], [-36, -284], [-122, 58], [-80, -269], [-26, -203], [-42, 346], [-75, 148], [-50, -12], [8, 162], [-173, 337], [-63, -29], [-99, -239], [-82, 107], [-31, 383], [48, 418]], [[6327, 99988], [-47, -101], [48, -169], [224, -155], [17, -194], [-74, -188], [-67, -17], [37, -231], [78, -55], [38, 244], [114, 378], [141, -247], [-26, -256], [158, -182], [67, 205], [106, -22], [59, 80], [270, -137], [31, -75], [-82, -245], [19, -120], [103, -89], [-123, 26], [-31, -65], [242, 15], [-62, -225], [147, -18], [77, -114], [0, 132], [108, 104], [114, -12], [-11, -144], [84, 40], [63, 134], [113, 19], [225, -196], [83, -204], [65, 84], [97, -126], [-1, -104], [190, -130], [69, 43], [276, -29], [162, -334], [198, -33], [146, 197], [213, 59], [196, -197], [91, -299], [173, -179], [67, -213], [114, 1], [0, -14159], [0, -3610], [6, -48], [125, -147], [31, 142], [112, -201], [109, 279], [146, 24], [8, -89], [-30, -431], [50, -184], [85, -159], [28, -266], [274, -980], [37, -471], [-1, -175], [180, 472], [62, 12], [42, 254], [0, 331], [52, 48], [-20, 201], [119, 142], [122, 240], [131, -444], [-23, -268], [29, -70], [17, -212], [63, -58], [71, -271], [37, -370], [27, -116], [98, -204], [68, -361], [65, -271], [6, -215], [57, -274], [26, -265], [44, -243], [102, -726], [89, -576], [-31, -222], [87, -99], [-22, -323], [65, -124], [21, -392], [59, 14], [136, -357], [72, -67], [54, -210], [42, -57], [26, -210], [80, -31], [28, -271], [-44, -407], [7, -386], [45, -501], [-32, -102], [-73, -615], [-85, -258], [-18, 113], [-37, -111], [-19, 98], [-1, 256], [-20, 88], [55, 270], [-52, -117], [-16, 216], [33, 168], [86, 7], [-11, 113], [-57, -15], [-3, 698], [-88, 500], [53, 269], [-93, -289], [-19, 94], [-91, -235], [-44, -60], [32, -57], [-12, -300], [-39, -318], [-57, 167], [-28, 320], [28, -64], [26, 152], [9, 289], [28, 100], [-6, 286], [90, 94], [-65, 32], [-26, 254], [-53, 45], [-12, 173], [-40, 110], [12, 212], [-48, -68], [-18, 293], [-62, 166], [-45, 262], [13, 73], [-189, 305], [31, 196], [53, -6], [-39, 103], [-31, 288], [19, 158], [-47, 18], [-12, 236], [20, 25], [113, -261], [3, 92], [-108, 261], [-2, 287], [115, -115], [-18, 76], [-98, 66], [-36, -264], [-48, 316], [44, -32], [9, 148], [-66, -64], [-42, 168], [-8, 237], [29, 227], [-6, 177], [-66, -370], [-71, 266], [-66, 38], [-51, 536], [-10, 314], [-20, -158], [-58, 740], [-37, 276], [9, 344], [-23, -287], [-39, 36], [54, -202], [-54, 32], [48, -247], [-1, -279], [61, -609], [-17, -42], [48, -497], [-11, -217], [-60, 28], [-42, 259], [-46, 73], [-79, -86], [17, 315], [-63, 506], [17, 88], [-37, 283], [14, -248], [-31, -240], [-68, 140], [-12, 279], [-39, -151], [-111, 335], [34, -216], [-29, -60], [60, -157], [17, 64], [71, -141], [-23, -26], [63, -180], [-32, -130], [35, -44], [9, 131], [47, -187], [29, -348], [-97, -169], [-63, 114], [10, -168], [-28, -97], [-60, 320], [-71, 25], [-126, 379], [29, 157], [-52, -94], [-73, 309], [-15, 237], [-85, 306], [-60, 84], [15, 127], [-57, -52], [-91, 211], [-63, 85], [-181, 432], [79, 130], [28, 196], [-41, 329], [41, 197], [46, -246], [-9, -275], [34, 287], [-81, 334], [-21, -174], [-74, -258], [-131, -225], [-82, 14], [-226, 311], [47, 233], [-34, 282], [-28, -60], [26, -145], [-97, -136], [-65, 123], [-231, 161], [-111, -85], [-115, -35], [-86, -100], [-93, 300], [17, 75], [-106, -6], [-59, 219], [-44, 2], [41, 301], [-11, 116], [20, 280], [-132, -580], [-43, -11], [-36, 140], [-117, 38], [88, 394], [-62, -79], [-19, 157], [-80, -128], [-18, 44], [58, 177], [-71, -15], [-35, -93], [-57, 4], [125, 233], [-88, -3], [-48, 116], [-20, 161], [29, 209], [102, 14], [-12, 105], [-73, 0], [-100, -356], [-28, 115], [-112, -204], [-27, 20], [10, 450], [-34, -327], [10, -207], [-37, 39], [-40, -99], [-49, 214], [46, 323], [51, 178], [-14, 120], [-89, -479], [-28, -2], [-45, -389], [-68, -71], [-35, -201], [74, 197], [21, -67], [-20, -221], [47, 252], [18, -252], [-32, -150], [11, -105], [54, 210], [41, -274], [-143, -528], [51, 92], [21, -186], [-40, 68], [-15, -213], [-50, -193], [-86, -38], [-65, 61], [19, 159], [-45, -84], [-25, -230], [-3, 258], [-37, 174], [12, -223], [-66, -322], [-31, 229], [-3, -536], [-48, 213], [-26, -287], [-45, -64], [0, -119], [-54, -120], [40, 469], [-71, -473], [-27, 28], [-95, -296], [-13, -175], [-24, 123], [-49, 33], [36, -129], [-17, -78], [-71, 98], [-91, -175], [-42, 101], [-26, 176], [23, 164], [69, 166], [53, -15], [54, 156], [77, 417], [-31, 14], [-102, -287], [-100, 153], [-14, 134], [49, 486], [67, 278], [52, 612], [-37, 368], [45, 139], [49, 19], [87, 270], [86, 204], [33, -2], [30, -182], [51, -39], [62, 117], [51, -74], [142, -106], [5, 85], [-62, 4], [-79, 122], [-125, 307], [48, 145], [66, 337], [93, 134], [-29, 65], [-93, -114], [-63, -395], [-130, 12], [-25, 223], [-52, -220], [-93, -173], [-26, -188], [-114, -133], [-51, -148], [-14, -169], [25, -195], [-45, 63], [-51, -163], [-79, -375], [25, -153], [-102, -327], [-77, 52], [83, -304], [-40, -350], [-137, -76], [-17, -92], [77, 23], [-12, -232], [-40, -107], [-70, 264], [-6, -248], [-66, -58], [38, -44], [-61, -126], [10, -195], [-108, -113], [33, -75], [-33, -114], [-15, -207], [9, -281], [40, 111], [93, -38], [99, -248], [-30, -381], [-71, -213], [-57, -10], [-9, -200], [-43, -31], [23, -209], [-53, -47], [-44, -201], [52, -73], [-38, -39], [-10, -212], [-29, 113], [-40, -228], [-31, 84], [-26, -124], [-70, 26], [-9, -229], [-71, -110], [-18, -217], [-59, 127], [-3, -241], [-43, -40], [7, -176], [-57, -34], [-20, 81], [-11, -279], [-30, 82], [-47, -111], [-57, -259], [54, 62], [-28, -156], [28, -99], [-57, -371], [-63, 131], [-14, -294], [-31, 32], [-69, -263], [-65, 174], [4, -179], [-46, -108], [33, -140], [-59, -61], [-25, 120], [-45, -39], [-60, -235], [70, 76], [1, -176], [-77, 67], [3, -91], [-77, -36], [-60, -282], [45, 85], [74, -113], [-39, -104], [8, -104], [-50, -130], [19, -190], [-48, 65], [31, 296], [-60, -171], [11, -261], [-54, 83], [-19, -175], [-43, 10], [-88, -127], [-45, 39], [14, -227], [-32, -347], [0, 430], [-62, 89], [-86, -218], [6, -118], [-87, -53], [22, -64], [-46, -262], [-19, 249], [-23, -270], [-28, -33], [-40, 137], [-28, -175], [-71, -138], [-66, 54], [37, 360], [57, -93], [-71, 220], [-51, -49], [-28, -191], [0, -228], [-52, -334], [-40, 22], [26, -197], [-28, -90], [-48, 65], [8, -139], [-69, 1], [-19, 142], [43, -18], [-61, 373], [-39, -159], [29, -98], [16, -391], [-31, 171], [-31, -219], [-39, 41], [-33, 270], [-44, 65], [-6, -167], [48, -198], [-77, -243], [12, 335], [-15, 247], [86, 240], [-11, -133], [65, 102], [-17, 71], [67, 181], [44, 23], [-15, 162], [89, 455], [113, 347], [187, 261], [-59, -117], [82, -27], [54, 99], [5, -112], [-47, -83], [63, -334], [5, 328], [76, -34], [14, -141], [54, -45], [7, 126], [-78, 184], [-16, 104], [59, 547], [49, 210], [124, 366], [80, 95], [79, 256], [63, 151], [86, -151], [-14, 464], [22, 179], [83, 407], [59, 125], [45, 223], [72, 143], [23, -151], [0, 249], [-29, 26], [26, 866], [42, 244], [-31, 191], [19, 272], [123, 500], [28, 452], [-34, -147], [-110, -165], [-26, -99], [-162, -255], [-31, 18], [-40, 279], [-45, 116], [51, 396], [-20, 71], [-77, -385], [-22, -246], [-29, 58], [43, -510], [-18, -195], [-62, 38], [-94, 630], [-56, 289], [-43, 40], [-14, -244], [-32, -67], [-39, 224], [-34, -35], [-46, 166], [-6, 227], [-116, -332], [-17, -160], [-15, 137], [-82, -194], [-31, 19], [-20, -253], [-111, -191], [15, 104], [-103, 13], [-12, 82], [74, 11], [-2, 89], [57, 172], [-34, 97], [-7, 306], [54, 119], [-8, 117], [-71, -127], [-43, 371], [29, 224], [65, 216], [-102, 717], [-74, 650], [-12, 210], [46, 492], [86, 240], [-102, -192], [-38, -439], [-53, -105], [39, -233], [-17, -395], [-62, 14], [-17, -119], [-77, -159], [-148, -111], [-72, -2], [-70, 82], [-23, 214], [26, 95], [-65, 117], [-74, 315], [-103, 209], [-43, 208], [32, 35], [5, 163], [-59, -106], [-60, 103], [122, 302], [-10, 144], [102, 256], [0, -183], [110, -72], [31, -250], [57, 235], [42, -86], [3, -287], [92, 176], [16, 145], [-39, 109], [-88, 86], [79, 124], [-40, 23], [6, 129], [-85, -262], [-156, -14], [-6, 105], [-74, 78], [-63, -64], [-33, 116], [68, 79], [-114, 368], [38, 88], [19, 304], [88, 153], [-68, -57], [-57, -257], [18, -117], [-49, -147], [8, -236], [-70, 137], [0, 260], [-57, 62], [-26, 165], [40, 222], [-40, 88], [-51, -121], [-26, 166], [23, 96], [93, 91], [-42, 48], [-48, 213], [139, 58], [-43, 332], [136, 784], [71, 197], [88, 136], [63, 254], [-93, -239], [-20, 422], [60, 362], [93, -44], [-12, 118], [-62, 113], [65, 189], [61, 63], [116, -87], [26, -136], [79, -218], [73, 51], [80, 308], [57, 109], [100, 514], [59, -114], [-40, -105], [274, 141], [110, 459], [8, 148], [-45, 427], [-14, 385], [-126, 348], [-30, -81], [26, 264], [98, -51], [80, 268], [-23, 318], [-82, 246], [-60, -291], [-90, 16], [-49, -159], [-51, 18], [-49, -183], [-98, -226], [-10, -214], [-39, -96], [-12, 299], [-116, 282], [-42, -98], [99, -145], [-28, -207], [-111, 289], [-79, 50], [-158, -40], [-73, -128], [-85, -15], [-23, -87], [-114, 120], [-207, 140], [-82, 275], [25, 322], [-82, 159], [-77, 328], [59, -73], [60, 41], [14, 185], [37, 77], [63, -79], [-3, 99], [-224, 190], [-128, 30], [-190, 363], [73, 245], [89, 39], [-17, 130], [60, 171], [81, -41], [51, 133], [-28, 52], [191, 391], [54, -104], [94, -38], [71, 90], [-108, 169], [34, 157], [128, 216], [76, -72], [88, 299], [125, 88], [150, 2], [-45, -389], [12, -209], [-94, -184], [80, 27], [62, -245], [91, 37], [65, -53], [94, 67], [40, -120], [70, -12], [58, 75], [101, -67], [66, 409], [131, -8], [12, -214], [19, 196], [-39, 194], [-103, 123], [-66, -4], [-59, -132], [25, 275], [-19, 144], [-91, 317], [-49, 16], [-56, 321], [87, 116], [86, -358], [-17, -202], [57, -256], [74, -149], [116, 157], [119, -316], [143, 61], [8, 247], [-60, 198], [-26, -82], [-98, 151], [-71, -39], [-49, -222], [-46, -2], [-108, 328], [-5, 195], [43, 276], [65, 32], [-94, 161], [-168, -122], [29, 305], [-63, -250], [-315, 159], [-22, 69], [-12, 373], [-42, 311], [-52, 230], [-151, 391], [-45, 47], [-162, 402], [-138, 146], [-103, 308], [-142, 116], [73, -2], [49, 155], [40, 266], [-3, 561], [159, -24], [173, 38], [196, 91], [105, 139], [95, 222], [139, 516], [22, 566], [28, 197], [122, 355], [168, 654], [49, -133], [108, 16], [135, 168], [196, 494], [58, -18], [6, -205], [-48, -320], [59, 148], [23, 190], [-18, 191], [-64, 84], [73, 122], [46, 186], [139, 93], [-77, -146], [128, 0], [264, 114], [134, 218], [91, 228], [116, 448], [107, 191]]]}