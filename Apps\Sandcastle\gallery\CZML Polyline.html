<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="CZML Polyline" />
    <meta name="cesium-sandcastle-labels" content="CZML" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>

    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const czml = [
          {
            id: "document",
            name: "CZML Geometries: Polyline",
            version: "1.0",
          },
          {
            id: "redLine",
            name: "Red line clamped to terain",
            polyline: {
              positions: {
                cartographicDegrees: [-75, 35, 0, -125, 35, 0],
              },
              material: {
                solidColor: {
                  color: {
                    rgba: [255, 0, 0, 255],
                  },
                },
              },
              width: 5,
              clampToGround: true,
            },
          },
          {
            id: "blueLine",
            name: "Glowing blue line on the surface",
            polyline: {
              positions: {
                cartographicDegrees: [-75, 37, 0, -125, 37, 0],
              },
              material: {
                polylineGlow: {
                  color: {
                    rgba: [100, 149, 237, 255],
                  },
                  glowPower: 0.2,
                  taperPower: 0.5,
                },
              },
              width: 10,
            },
          },
          {
            id: "orangeLine",
            name: "Orange line with black outline at height and following the surface",
            polyline: {
              positions: {
                cartographicDegrees: [-75, 39, 250000, -125, 39, 250000],
              },
              material: {
                polylineOutline: {
                  color: {
                    rgba: [255, 165, 0, 255],
                  },
                  outlineColor: {
                    rgba: [0, 0, 0, 255],
                  },
                  outlineWidth: 2,
                },
              },
              width: 5,
            },
          },
          {
            id: "purpleLine",
            name: "Purple arrow at height",
            polyline: {
              positions: {
                cartographicDegrees: [-75, 43, 500000, -125, 43, 500000],
              },
              material: {
                polylineArrow: {
                  color: {
                    rgba: [148, 0, 211, 255],
                  },
                },
              },
              arcType: "NONE",
              width: 10,
            },
          },
          {
            id: "dashedLine",
            name: "Blue dashed line",
            polyline: {
              positions: {
                cartographicDegrees: [-75, 45, 500000, -125, 45, 500000],
              },
              material: {
                polylineDash: {
                  color: {
                    rgba: [0, 255, 255, 255],
                  },
                },
              },
              width: 4,
            },
          },
        ];

        const viewer = new Cesium.Viewer("cesiumContainer");
        const dataSourcePromise = Cesium.CzmlDataSource.load(czml);
        viewer.dataSources.add(dataSourcePromise);
        viewer.zoomTo(dataSourcePromise);

        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
