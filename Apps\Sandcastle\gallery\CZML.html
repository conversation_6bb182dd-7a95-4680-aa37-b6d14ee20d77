<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="A simple CZML example showing four satellites in orbit around the Earth, and some ground objects."
    />
    <meta name="cesium-sandcastle-labels" content="Showcases, DataSources" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>

    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer", {
          shouldAnimate: true,
        });

        Sandcastle.addDefaultToolbarButton("Satellites", function () {
          viewer.dataSources.add(
            Cesium.CzmlDataSource.load("../../SampleData/simple.czml"),
          );

          viewer.camera.flyHome(0);
        });

        Sandcastle.addToolbarButton("Vehicle", function () {
          viewer.dataSources.add(
            Cesium.CzmlDataSource.load("../../SampleData/Vehicle.czml"),
          );

          viewer.scene.camera.setView({
            destination: Cesium.Cartesian3.fromDegrees(-116.52, 35.02, 95000),
            orientation: {
              heading: 6,
            },
          });
        });

        Sandcastle.reset = function () {
          viewer.dataSources.removeAll();
        };
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
