<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="Draw polylines with different color appearances." />
    <meta name="cesium-sandcastle-labels" content="Development" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        // Create the viewer.
        const viewer = new Cesium.Viewer("cesiumContainer");
        const scene = viewer.scene;

        // Example 1: Draw a polyline with per segment colors
        let positions = [];
        let colors = [];
        let i;
        for (i = 0; i < 12; ++i) {
          positions.push(Cesium.Cartesian3.fromDegrees(-124.0 + 5 * i, 40.0));
          colors.push(Cesium.Color.fromRandom({ alpha: 1.0 }));
        }
        // For per segment coloring, supply the colors option with
        // an array of colors for each segment.
        scene.primitives.add(
          new Cesium.Primitive({
            geometryInstances: new Cesium.GeometryInstance({
              geometry: new Cesium.PolylineGeometry({
                positions: positions,
                width: 5.0,
                vertexFormat: Cesium.PolylineColorAppearance.VERTEX_FORMAT,
                colors: colors,
              }),
            }),
            appearance: new Cesium.PolylineColorAppearance(),
          }),
        );

        // Example 2: Draw a polyline with per vertex colors
        positions = [];
        colors = [];
        for (i = 0; i < 12; ++i) {
          positions.push(Cesium.Cartesian3.fromDegrees(-124.0 + 5 * i, 35.0));
          colors.push(Cesium.Color.fromRandom({ alpha: 1.0 }));
        }
        // For per segment coloring, supply the colors option with
        // an array of colors for each vertex.  Also set the
        // colorsPerVertex option to true.
        scene.primitives.add(
          new Cesium.Primitive({
            geometryInstances: new Cesium.GeometryInstance({
              geometry: new Cesium.PolylineGeometry({
                positions: positions,
                width: 5.0,
                vertexFormat: Cesium.PolylineColorAppearance.VERTEX_FORMAT,
                colors: colors,
                colorsPerVertex: true,
              }),
            }),
            appearance: new Cesium.PolylineColorAppearance(),
          }),
        );
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
