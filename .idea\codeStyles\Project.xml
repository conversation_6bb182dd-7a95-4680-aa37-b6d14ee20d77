<component name="ProjectCodeStyleConfiguration">
  <code_scheme name="Project" version="173">
    <JSCodeStyleSettings version="0">
      <option name="SPACE_BEFORE_PROPERTY_COLON" value="true" />
      <option name="SPACE_BEFORE_FUNCTION_LEFT_PARENTH" value="false" />
    </JSCodeStyleSettings>
    <codeStyleSettings language="JavaScript">
      <option name="KEEP_BLANK_LINES_IN_CODE" value="1" />
      <option name="ALIGN_MULTILINE_BINARY_OPERATION" value="true" />
      <option name="ALIGN_MULTILINE_TERNARY_OPERATION" value="true" />
      <option name="ALIGN_MULTILINE_ARRAY_INITIALIZER_EXPRESSION" value="true" />
      <option name="METHOD_PARAMETERS_LPAREN_ON_NEXT_LINE" value="true" />
      <option name="IF_BRACE_FORCE" value="3" />
    </codeStyleSettings>
  </code_scheme>
</component>