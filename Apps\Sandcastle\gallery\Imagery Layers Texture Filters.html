<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="Set the texture minification and magnification filters of an imagery layer."
    />
    <meta name="cesium-sandcastle-labels" content="Beginner, Tutorials, Showcases" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);

      #slider {
        position: absolute;
        left: 50%;
        top: 0px;
        background-color: #d3d3d3;
        width: 2px;
        height: 100%;
        z-index: 9999;
      }

      #slider:hover {
        cursor: ew-resize;
      }

      .split-label {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(42, 42, 42, 0.8);
        padding: 8px;
        border-radius: 4px;
        z-index: 9999;
        font-size: large;
        font-weight: bold;
        text-align: center;
      }
      .split-label.left {
        left: 0;
      }
      .split-label.right {
        right: 0;
      }
    </style>

    <div id="cesiumContainer" class="fullSize">
      <div id="slider"></div>
      <div class="split-label left">Magnification<br />Linear</div>
      <div class="split-label right">Magnification<br />Nearest</div>
    </div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>

    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer");
        viewer.camera.flyTo({
          destination: new Cesium.Rectangle.fromDegrees(-84, 43, -80, 47),
        });

        const layers = viewer.imageryLayers;
        layers.removeAll();

        const layerLinear = Cesium.ImageryLayer.fromProviderAsync(
          Cesium.TileMapServiceImageryProvider.fromUrl(
            Cesium.buildModuleUrl("Assets/Textures/NaturalEarthII"),
          ),
        );
        layers.add(layerLinear);

        const layerNearest = Cesium.ImageryLayer.fromProviderAsync(
          Cesium.TileMapServiceImageryProvider.fromUrl(
            Cesium.buildModuleUrl("Assets/Textures/NaturalEarthII"),
          ),
        );
        layers.add(layerNearest);

        // Set the texture minification and magnification filters of layerNearest. Default is LINEAR.
        layerNearest.minificationFilter = Cesium.TextureMinificationFilter.NEAREST;
        layerNearest.magnificationFilter = Cesium.TextureMagnificationFilter.NEAREST;

        // The remaining code installs a split layer so the effect of the texture filters becomes apparent.

        layerNearest.splitDirection = Cesium.SplitDirection.RIGHT;

        const slider = document.getElementById("slider");
        viewer.scene.splitPosition = slider.offsetLeft / slider.parentElement.offsetWidth;

        let dragStartX = 0;

        document.getElementById("slider").addEventListener("mousedown", mouseDown, false);
        window.addEventListener("mouseup", mouseUp, false);

        function mouseUp() {
          window.removeEventListener("mousemove", sliderMove, true);
        }

        function mouseDown(e) {
          const slider = document.getElementById("slider");
          dragStartX = e.clientX - slider.offsetLeft;
          window.addEventListener("mousemove", sliderMove, true);
        }

        function sliderMove(e) {
          const slider = document.getElementById("slider");
          const splitPosition =
            (e.clientX - dragStartX) / slider.parentElement.offsetWidth;
          slider.style.left = `${100.0 * splitPosition}%`;
          viewer.scene.splitPosition = splitPosition;
        }
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
