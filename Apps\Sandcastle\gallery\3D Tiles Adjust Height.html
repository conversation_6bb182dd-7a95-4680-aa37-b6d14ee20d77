<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />
    <!-- Use Chrome Frame in IE -->
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="Adjust the height of a 3D Tiles tileset." />
    <meta name="cesium-sandcastle-labels" content="3D Tiles" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
      #toolbar {
        background: rgba(42, 42, 42, 0.8);
        padding: 4px;
        border-radius: 4px;
      }
      #toolbar input {
        vertical-align: middle;
        padding-top: 2px;
        padding-bottom: 2px;
      }
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar">
      <div>Height</div>
      <input
        type="range"
        min="-100.0"
        max="100.0"
        step="1"
        data-bind="value: height, valueUpdate: 'input'"
      />
      <input type="text" size="5" data-bind="value: height" />
    </div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer", {
          shadows: true,
        });

        const viewModel = {
          height: 0,
        };

        Cesium.knockout.track(viewModel);

        const toolbar = document.getElementById("toolbar");
        Cesium.knockout.applyBindings(viewModel, toolbar);

        let tileset;
        try {
          tileset = await Cesium.Cesium3DTileset.fromUrl(
            "../../SampleData/Cesium3DTiles/Tilesets/Tileset/tileset.json",
          );
          viewer.scene.primitives.add(tileset);
          viewer.scene.globe.depthTestAgainstTerrain = true;
          viewer.zoomTo(
            tileset,
            new Cesium.HeadingPitchRange(0.0, -0.5, tileset.boundingSphere.radius * 2.0),
          );
        } catch (error) {
          console.log(`Error loading tileset: ${error}`);
        }

        Cesium.knockout.getObservable(viewModel, "height").subscribe(function (height) {
          height = Number(height);
          if (isNaN(height) || !Cesium.defined(tileset)) {
            return;
          }

          const cartographic = Cesium.Cartographic.fromCartesian(
            tileset.boundingSphere.center,
          );
          const surface = Cesium.Cartesian3.fromRadians(
            cartographic.longitude,
            cartographic.latitude,
            0.0,
          );
          const offset = Cesium.Cartesian3.fromRadians(
            cartographic.longitude,
            cartographic.latitude,
            height,
          );
          const translation = Cesium.Cartesian3.subtract(
            offset,
            surface,
            new Cesium.Cartesian3(),
          );
          tileset.modelMatrix = Cesium.Matrix4.fromTranslation(translation);
        });

        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
