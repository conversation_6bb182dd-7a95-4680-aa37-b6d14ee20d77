<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="A simple KML example." />
    <meta name="cesium-sandcastle-labels" content="Showcases, DataSources" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>

    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer");
        const options = {
          camera: viewer.scene.camera,
          canvas: viewer.scene.canvas,
          screenOverlayContainer: viewer.container,
        };

        Sandcastle.addToolbarMenu(
          [
            {
              text: "KML - Global Science Facilities",
              onselect: function () {
                viewer.camera.flyHome(0);
                viewer.dataSources.add(
                  Cesium.KmlDataSource.load(
                    "../../SampleData/kml/facilities/facilities.kml",
                    options,
                  ),
                );
              },
            },
            {
              text: "KMZ with embedded data - GDP per capita",
              onselect: function () {
                viewer.camera.flyHome(0);
                viewer.dataSources.add(
                  Cesium.KmlDataSource.load(
                    "../../SampleData/kml/gdpPerCapita2008.kmz",
                    options,
                  ),
                );
              },
            },
            {
              text: "gx KML extensions - Bike Ride",
              onselect: function () {
                viewer.dataSources
                  .add(
                    Cesium.KmlDataSource.load(
                      "../../SampleData/kml/bikeRide.kml",
                      options,
                    ),
                  )
                  .then(function (dataSource) {
                    viewer.clock.shouldAnimate = false;
                    const rider = dataSource.entities.getById("tour");
                    viewer.flyTo(rider).then(function () {
                      viewer.trackedEntity = rider;
                      viewer.selectedEntity = viewer.trackedEntity;
                      viewer.clock.multiplier = 30;
                      viewer.clock.shouldAnimate = true;
                    });
                  });
              },
            },
          ],
          "toolbar",
        );

        Sandcastle.reset = function () {
          viewer.dataSources.removeAll();
          viewer.clock.clockRange = Cesium.ClockRange.UNBOUNDED;
          viewer.clock.clockStep = Cesium.ClockStep.SYSTEM_CLOCK;
        };
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
