<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />
    <!-- Use Chrome Frame in IE -->
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="A sample interior rendered with 3D Tiles." />
    <meta name="cesium-sandcastle-labels" content="Showcases, 3D Tiles" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        // San Miguel model created by Guillermo M. Leal Llaguno. Cleaned up and hosted by Morgan McGuire: http://graphics.cs.williams.edu/data/meshes.xml
        const viewer = new Cesium.Viewer("cesiumContainer");

        try {
          const tileset = await Cesium.Cesium3DTileset.fromIonAssetId(125737);
          viewer.scene.primitives.add(tileset);
        } catch (error) {
          console.log(`Error loading tileset: ${error}`);
        }

        const initialPosition = new Cesium.Cartesian3(
          -1111583.3721328347,
          -5855888.151574568,
          2262561.444696748,
        );
        const initialOrientation = new Cesium.HeadingPitchRoll.fromDegrees(
          100.0,
          -15.0,
          0.0,
        );
        viewer.scene.camera.setView({
          destination: initialPosition,
          orientation: initialOrientation,
          endTransform: Cesium.Matrix4.IDENTITY,
        });
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
