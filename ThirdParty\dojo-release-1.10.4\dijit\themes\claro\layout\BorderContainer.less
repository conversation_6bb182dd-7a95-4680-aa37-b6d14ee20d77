/* BorderContainer 

Splitters and gutters separate panes within a BorderContainer. Splitters can be moved up and down (horizonal splitters) or left and right (vertical splitters), while Gutters are static. A "thumb" is the slit on a Splitter that indicates it is movable. 

Styling the BorderContainer widget consists of the following: 

.dijitBorderContainer - for border and padding of the entire border container

.dijitSplitContainer-child, .dijitBorderContainer-child - for border or child panes of the border container. By default borders are put on all children of BorderContainer.  

.dijitBorderContainer-dijitTabContainerTop,
.dijitBorderContainer-dijitTabContainerBottom,
.dijitBorderContainer-dijitTabContainerLeft,
.dijitBorderContainer-dijitTabContainerRight,
.dijitBorderContainer-dijitAccordionContainer   -for border of the border containers within TabContainer or AccordionContainer widget

.dijitBorderContainer-dijitBorderContainer - for border and padding of nested BorderContainers

Splitters and gutters:  

.dijitSplitterH, .dijitGutterH - for height, background, and border of a horizontal splitter and gutter 
.dijitSplitterH .dijitSplitterThumb - for color, height/width of the thumb on a horizontal splitter
.dijitSplitterV, .dijitGutterV - - for height, background, and border of a vertical splitter and gutter 
.dijitSplitterV .dijitSplitterThumb - for color, height/width of the thumb on a vertical splitter
.dijitSplitterHHover - for background-color of a hovered horizontal splitter
.dijitSplitterHHover .dijitSplitterThumb - for background-color of a hovered thumb on a horizontal splitter
.dijitSplitterVHover  - for background-color of a hovered vertical splitter
.dijitSplitterVHover .dijitSplitterThumb - for background-color of a hovered thumb on a vertical splitter
.dijitSplitterHActive - for background-color of an active horizontal splitter
.dijitSplitterVActive - for background-color of an active horizontal splitter
*/

@import "../variables";

.claro .dijitBorderContainer {
	/* matches the width of the splitters between panes */
	padding: 5px;
}

.claro .dijitSplitContainer-child,
.claro .dijitBorderContainer-child {
	/* By default put borders on all children of BorderContainer,
	 *  to give illusion of borders on the splitters themselves.
	 */
	border: 1px @border-color solid;
}

.claro .dijitBorderContainer-dijitTabContainerTop,
.claro .dijitBorderContainer-dijitTabContainerBottom,
.claro .dijitBorderContainer-dijitTabContainerLeft,
.claro .dijitBorderContainer-dijitTabContainerRight,
.claro .dijitBorderContainer-dijitAccordionContainer {
	/* except that TabContainer defines borders on it's sub-nodes (tablist and dijitTabPaneWrapper),
	 * so override rule setting border on domNode
	 */
	 border: none;

}
.claro .dijitBorderContainer-dijitBorderContainer {
	/* make nested BorderContainers look like a single big widget with lots of splitters */
	border: 0;
	padding: 0;
}

/* Splitters and gutters */

.claro .dijitSplitterH,
.claro .dijitGutterH {
	background:none;
	border:0;
	height:5px;
}
.dj_ios .claro .dijitSplitterH, .dj_android .claro .dijitSplitterH {
  height: 11px;
}

.claro .dijitSplitterH .dijitSplitterThumb {
	background:@border-color none;
	height:1px;
	top:2px;
	width:19px;
}
.dj_ios .claro .dijitSplitterH .dijitSplitterThumb, .dj_android .claro .dijitSplitterH .dijitSplitterThumb{
  top:5px;
}
.claro .dijitSplitterV,
.claro .dijitGutterV {
	background:none;
	border:0;
	width:5px;
	margin: 0;
}
.dj_ios .claro .dijitSplitterV, .dj_android .claro .dijitSplitterV {
  width: 11px;
}

.claro .dijitSplitterV .dijitSplitterThumb {
	background:@border-color none;
	height:19px;
	left:2px;
	width:1px;
	margin: 0;
}
.dj_ios .claro .dijitSplitterV .dijitSplitterThumb, .dj_android .claro .dijitSplitterV .dijitSplitterThumb{
  left:5px;
}

/* hovered splitter */
.claro .dijitSplitterHHover,
.claro .dijitSplitterVHover {
	font-size: 1px;
	background-color: @splitter-hovered-background-color;
}

.claro .dijitSplitterHHover {
	.alpha-white-gradient (left, 1,0px,  0,50%, 1,100%);
}

.claro .dijitSplitterVHover {
	.alpha-white-gradient (top, 1,0px,  0,50%, 1,100%);
}

.claro .dijitSplitterHHover .dijitSplitterThumb,
.claro .dijitSplitterVHover .dijitSplitterThumb {
	background:@hovered-border-color none;
}


/* active splitter */
.claro .dijitSplitterHActive,
.claro .dijitSplitterVActive {
	font-size: 1px;
	background-color:@splitter-dragged-background-color;
	background-image: none;		// color all the way across, not gradient like in hover mode
}
