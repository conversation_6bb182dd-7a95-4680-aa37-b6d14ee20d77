<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="Draw the outline of a box." />
    <meta name="cesium-sandcastle-labels" content="Development" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        // Create the viewer.
        const viewer = new Cesium.Viewer("cesiumContainer");
        const scene = viewer.scene;

        // Draw the outline of a box.

        const dimensions = new Cesium.Cartesian3(400000.0, 400000.0, 400000.0);

        // Box geometries are initially centered on the origin.
        // We can use a model matrix to position the box on the
        // globe surface.
        const positionOnEllipsoid = Cesium.Cartesian3.fromDegrees(-106.0, 45.0);
        const boxModelMatrix = Cesium.Matrix4.multiplyByTranslation(
          Cesium.Transforms.eastNorthUpToFixedFrame(positionOnEllipsoid),
          new Cesium.Cartesian3(0.0, 0.0, dimensions.z * 0.5),
          new Cesium.Matrix4(),
        );

        // Create a box outline geometry.
        const boxOutlineGeometry = Cesium.BoxOutlineGeometry.fromDimensions({
          dimensions: dimensions,
        });

        // Create a geometry instance using the geometry
        // and model matrix created above.
        const boxOutlineInstance = new Cesium.GeometryInstance({
          geometry: boxOutlineGeometry,
          modelMatrix: boxModelMatrix,
          attributes: {
            color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.WHITE),
          },
        });

        // Add the geometry instance to primitives.
        scene.primitives.add(
          new Cesium.Primitive({
            geometryInstances: boxOutlineInstance,
            appearance: new Cesium.PerInstanceColorAppearance({
              flat: true,
              renderState: {
                lineWidth: Math.min(2.0, scene.maximumAliasedLineWidth),
              },
            }),
          }),
        );
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
