<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="Display conditions" />
    <meta name="cesium-sandcastle-labels" content="Development" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer");
        const scene = viewer.scene;

        function addBillboardAndPrimitive() {
          Sandcastle.declare(addBillboardAndPrimitive);

          const billboards = scene.primitives.add(new Cesium.BillboardCollection());
          billboards.add({
            image: "../images/facility.gif",
            position: Cesium.Cartesian3.fromDegrees(-77, 40.5),
            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(5.5e6),
          });

          scene.primitives.add(
            new Cesium.Primitive({
              geometryInstances: new Cesium.GeometryInstance({
                geometry: new Cesium.RectangleGeometry({
                  rectangle: Cesium.Rectangle.fromDegrees(-80.5, 39.7, -75.1, 42.0),
                  vertexFormat: Cesium.PerInstanceColorAppearance.VERTEX_FORMAT,
                }),
                attributes: {
                  color: Cesium.ColorGeometryInstanceAttribute.fromColor(
                    new Cesium.Color(1.0, 0.0, 0.0, 0.5),
                  ),
                  distanceDisplayCondition:
                    new Cesium.DistanceDisplayConditionGeometryInstanceAttribute(
                      0.0,
                      5.5e6,
                    ),
                },
              }),
              appearance: new Cesium.PerInstanceColorAppearance({
                closed: true,
              }),
            }),
          );
        }

        async function addPointAndModel() {
          Sandcastle.declare(addPointAndModel);

          const position = Cesium.Cartesian3.fromDegrees(-75.59777, 40.03883);

          const pointPrimitives = scene.primitives.add(
            new Cesium.PointPrimitiveCollection(),
          );
          pointPrimitives.add({
            color: Cesium.Color.YELLOW,
            position: position,
            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(250.5),
          });

          try {
            scene.primitives.add(
              await Cesium.Model.fromGltfAsync({
                url: "../../SampleData/models/GroundVehicle/GroundVehicle.glb",
                modelMatrix: Cesium.Transforms.eastNorthUpToFixedFrame(position),
                distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0.0, 250.5),
              }),
            );
          } catch (error) {
            window.alert(error);
          }
        }

        Sandcastle.addToolbarMenu([
          {
            text: "Billboard and Primitive",
            onselect: function () {
              addBillboardAndPrimitive();
              Sandcastle.highlight(addBillboardAndPrimitive);
            },
          },
          {
            text: "Point and Model",
            onselect: function () {
              addPointAndModel();
              Sandcastle.highlight(addPointAndModel);
            },
          },
        ]);

        Sandcastle.reset = function () {
          viewer.camera.flyHome(0);
          scene.primitives.removeAll();
        };

        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
