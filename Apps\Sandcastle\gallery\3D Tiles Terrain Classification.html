<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />
    <!-- Use Chrome Frame in IE -->
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="A sample Vector dataset on terrain rendered with 3D Tiles."
    />
    <meta name="cesium-sandcastle-labels" content="Showcases, 3D Tiles" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer", {
          terrain: Cesium.Terrain.fromWorldTerrain(),
        });

        const geocoder = viewer.geocoder.viewModel;
        geocoder.searchText = "Vienna, Austria";
        geocoder.flightDuration = 0.0;
        geocoder.search();

        try {
          // Vector 3D Tiles are experimental and the format is subject to change in the future.
          // For more details, see:
          //    https://github.com/CesiumGS/3d-tiles/tree/vctr/TileFormats/VectorData
          const tileset = await Cesium.Cesium3DTileset.fromIonAssetId(5737);
          viewer.scene.primitives.add(tileset);

          tileset.style = new Cesium.Cesium3DTileStyle({
            color: "rgba(255, 255, 255, 0.5)",
          });
        } catch (error) {
          console.log(`Error loading tileset: ${error}`);
        }

        // Information about the currently highlighted feature
        const highlighted = {
          feature: undefined,
          originalColor: new Cesium.Color(),
        };

        // Color a feature yellow on hover.
        viewer.screenSpaceEventHandler.setInputAction(function onMouseMove(movement) {
          // If a feature was previously highlighted, undo the highlight
          if (Cesium.defined(highlighted.feature)) {
            highlighted.feature.color = highlighted.originalColor;
            highlighted.feature = undefined;
          }

          // Pick a new feature
          const pickedFeature = viewer.scene.pick(movement.endPosition);
          if (!Cesium.defined(pickedFeature)) {
            return;
          }

          // Highlight the feature
          highlighted.feature = pickedFeature;
          Cesium.Color.clone(pickedFeature.color, highlighted.originalColor);
          pickedFeature.color = Cesium.Color.YELLOW;
        }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
