<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="Change the order in which DataSources draw ground primitives"
    />
    <meta name="cesium-sandcastle-labels" content="DataSources" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const czml1 = [
          {
            id: "document",
            name: "CZML Geometries: Rectangle",
            version: "1.0",
          },
          {
            rectangle: {
              coordinates: {
                wsenDegrees: [-120, 40, -110, 50],
              },
              fill: true,
              material: {
                solidColor: {
                  color: {
                    rgba: [255, 0, 0, 255],
                  },
                },
              },
            },
          },
          {
            rectangle: {
              coordinates: {
                wsenDegrees: [-110, 40, -100, 50],
              },
              fill: true,
              material: {
                solidColor: {
                  color: {
                    rgba: [0, 0, 255, 255],
                  },
                },
              },
            },
          },
        ];

        const czml2 = [
          {
            id: "document",
            name: "CZML Geometries: Rectangle",
            version: "1.0",
          },
          {
            rectangle: {
              coordinates: {
                wsenDegrees: [-120, 45, -110, 55],
              },
              fill: true,
              material: {
                solidColor: {
                  color: {
                    rgba: [255, 255, 0, 255],
                  },
                },
              },
            },
          },
          {
            rectangle: {
              coordinates: {
                wsenDegrees: [-110, 45, -100, 55],
              },
              fill: true,
              material: {
                solidColor: {
                  color: {
                    rgba: [0, 255, 255, 255],
                  },
                },
              },
            },
          },
        ];

        const viewer = new Cesium.Viewer("cesiumContainer");
        const promise1 = Cesium.CzmlDataSource.load(czml1);
        viewer.dataSources.add(promise1);
        const promise2 = Cesium.CzmlDataSource.load(czml2);
        viewer.dataSources.add(promise2);

        Sandcastle.addToolbarButton("Swap", function () {
          Promise.all([promise1, promise2]).then(function (results) {
            const ds1 = results[0];
            const ds2 = results[1];
            if (viewer.dataSources.indexOf(ds1) === 0) {
              viewer.dataSources.raise(ds1);
            } else {
              viewer.dataSources.lower(ds1);
            }
          });
        }); //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
