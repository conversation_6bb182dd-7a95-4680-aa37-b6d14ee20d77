﻿<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>Timeline Demo</title>
    <style type="text/css">
      @import url("../../ThirdParty/dojo-release-1.10.4/dijit/themes/claro/claro.css");
      @import url("../../Source/Widgets/Animation/Animation.css");
      @import url("../../Source/Widgets/Animation/lighter.css");
      @import url("../../Source/Widgets/Timeline/Timeline.css");
      @import url("../../Source/Widgets/Timeline/lighter.css");

      body {
        background: #000;
        color: #c0cccc;
        font-family: "Open Sans", "Trebuchet MS", Sans-Serif;
        font-size: 15px;
        -webkit-transition: background-color, color;
        -webkit-transition-duration: 1s;
        -moz-transition: background-color, color;
        -moz-transition-duration: 1s;
        transition: background-color, color;
        transition-duration: 1s;
      }

      body.cesium-lighter {
        background: #fff;
        color: #000;
      }

      .startStopBlock {
        overflow: hidden; /* causes it to contain floating children. */
      }

      .dateTimeBlock {
        display: block;
        float: left;
        margin-right: 10px;
        border: solid 1px #888;
        border-radius: 25px;
        color: #000;
        background: #444;
      }
      .cesium-lighter .dateTimeBlock {
        background: #fff;
      }

      .info {
        display: block;
        float: left;
        margin-right: 10px;
        font-family: Monospace;
      }

      .timeSel {
        padding: 10px 20px;
        color: #c0cccc;
      }
      .cesium-lighter .timeSel {
        color: #000;
      }

      .dateTimeBlock .dijitCalendarContainer {
        margin: 5px auto 15px auto;
      }

      #timelineAndAnimation {
        display: block;
        clear: both;
        position: relative;
        top: 0;
        left: 0;
        width: 90%;
        height: 132px;
        margin: 20px auto 0;
        border-bottom: 1px solid #888;
      }

      .myAnimation {
        display: block;
        position: absolute;
        bottom: 0;
        left: 0;
        width: 169px;
        height: 111px;
        -webkit-transition: none;
        -webkit-transition-duration: 0;
        -moz-transition: none;
        -moz-transition-duration: 0;
        transition: none;
        transition-duration: 0;
      }

      .myTimeline {
        display: block;
        position: absolute;
        bottom: -1px;
        left: 169px;
        right: 0;
        height: 59px;
        overflow: hidden;
      }

      .usage {
        display: block;
        float: right;
        width: 250px;
        padding: 7px;
        border: solid 1px #aaa;
        border-radius: 5px;
        margin: 10px 15px;
      }

      .usage h2 {
        margin: 0 0 5px;
        padding: 0;
        border-bottom: solid 1px #aaa;
        text-align: center;
      }

      .action {
        font-family: monospace;
        text-align: right;
        white-space: nowrap;
        padding-right: 5px;
      }

      table {
        border: none;
        border-collapse: collapse;
      }

      tr {
        border-top: solid 1px #ddd;
      }

      .themeSelector {
        display: block;
        float: right;
      }

      #endBeforeStart {
        display: none;
        font-weight: bold;
        color: #f00;
      }
    </style>
    <script
      type="text/javascript"
      src="../../Build/CesiumUnminified/Cesium.js"
    ></script>
    <script
      data-dojo-config="async: 1, tlmSiblingOfDojo: 0"
      src="../../ThirdParty/dojo-release-1.10.4/dojo/dojo.js"
    ></script>
    <script type="text/javascript" src="boot.js"></script>
  </head>
  <body class="claro cesium-lighter">
    <div class="usage">
      <h2>Usage</h2>
      <p>
        The following mouse actions are available on the time bar itself, inside
        the area with the tic marks and time labels. All dragging is horizontal,
        not vertical.
      </p>
      <table>
        <tr>
          <td class="action">Left-click</td>
          <td>Jump to time</td>
        </tr>
        <tr>
          <td class="action">Left-drag</td>
          <td>Scroll through time</td>
        </tr>
        <tr>
          <td class="action">Right-drag</td>
          <td>Zoom</td>
        </tr>
        <tr>
          <td class="action">Wheel-spin</td>
          <td>Zoom</td>
        </tr>
        <tr>
          <td class="action">Middle-drag</td>
          <td>Pan, only after zooming in.</td>
        </tr>
      </table>
    </div>
    <div
      data-dojo-type="dijit.form.ComboButton"
      data-dojo-props='iconClass:"dijitIconSample", optionsTitle:"select theme"'
      id="themeSelector"
      class="themeSelector"
    >
      <span>Theme: Lighter</span>
      <div data-dojo-type="dijit.Menu" id="themeMenu" style="display: none">
        <div id="themeLighter" data-dojo-type="dijit.MenuItem">Lighter</div>
        <div id="themeDarker" data-dojo-type="dijit.MenuItem">Darker</div>
      </div>
    </div>
    <h1>Timeline Demo</h1>
    <p>
      This is a simple demo of the Cesium Timeline widget. Select a start and
      end date below to set the start and end dates of the timeline. You can
      click on the time bar at the top to jump to the time at that location. You
      can also click and drag the mouse back and forth to change time
      dynamically. The current time is shown at the bottom. You can also use the
      mouse wheel to zoom in and out, dynamically adjusting the visible time
      span.
    </p>
    <p>
      The colored bars show the ability for us to specify important intervals of
      time. Eventually these bars will gain additional functionality, such as
      click notification and pop-up menus.
    </p>
    <div class="startStopBlock">
      <div class="dateTimeBlock">
        <div class="timeSel">
          Start time:
          <input
            type="text"
            id="startTimeSel"
            data-dojo-type="dijit.form.TimeTextBox"
          />
        </div>
        <div id="startCal" data-dojo-type="dijit.Calendar"></div>
      </div>
      <div class="dateTimeBlock">
        <div class="timeSel">
          End time:
          <input
            type="text"
            id="endTimeSel"
            data-dojo-type="dijit.form.TimeTextBox"
          />
        </div>
        <div id="endCal" data-dojo-type="dijit.Calendar"></div>
      </div>
      <div class="info">
        <p>Visible Timespan: <span id="formatted">(none)</span></p>
        <p id="endBeforeStart">ERROR: Start time must come before end time.</p>
      </div>
    </div>

    <div id="timelineAndAnimation">
      <div id="time1" class="myTimeline"></div>
      <div id="animationWidget" class="myAnimation"></div>
    </div>

    <p class="info">Tick position: <span id="mousePos"></span></p>
  </body>
</html>
