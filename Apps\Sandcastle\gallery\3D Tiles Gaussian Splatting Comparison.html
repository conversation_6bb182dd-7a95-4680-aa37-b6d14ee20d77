<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />
    <!-- Use Chrome Frame in IE -->
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="View the same 3D Tiles power station dataset with Gaussian splatting (or Gaussian splats) rendering versus triangle-based mesh rendering."
    />
    <meta name="cesium-sandcastle-labels" content="Showcases, 3D Tiles" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);

      #slider {
        position: absolute;
        left: 50%;
        top: 0px;
        background-color: #d3d3d3;
        width: 5px;
        height: 100%;
        z-index: 9999;
      }

      #slider:hover {
        cursor: ew-resize;
      }
      .split-label {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(42, 42, 42, 0.8);
        padding: 8px;
        border-radius: 4px;
        z-index: 9999;
        font-size: large;
        font-weight: bold;
        text-align: center;
      }
      .split-label.left {
        left: 0;
      }
      .split-label.right {
        right: 0;
      }
    </style>
    <div id="cesiumContainer" class="fullSize">
      <div id="slider"></div>
      <div class="split-label left">3D Tiles<br />Gaussian splats</div>
      <div class="split-label right">3D Tiles<br />Mesh</div>
    </div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer", {
          terrain: Cesium.Terrain.fromWorldTerrain(),
        });

        try {
          const left = await Cesium.Cesium3DTileset.fromIonAssetId(3667784);
          viewer.scene.primitives.add(left);
          left.splitDirection = Cesium.SplitDirection.LEFT;

          viewer.zoomTo(
            left,
            new Cesium.HeadingPitchRange(
              Cesium.Math.toRadians(-50),
              Cesium.Math.toRadians(-20),
              100.0,
            ),
          );

          const right = await Cesium.Cesium3DTileset.fromIonAssetId(3443919);
          viewer.scene.primitives.add(right);
          right.splitDirection = Cesium.SplitDirection.RIGHT;
        } catch (error) {
          console.log(`Error loading tileset: ${error}`);
        }

        // Sync the position of the slider with the split position
        const slider = document.getElementById("slider");
        viewer.scene.splitPosition = slider.offsetLeft / slider.parentElement.offsetWidth;

        const handler = new Cesium.ScreenSpaceEventHandler(slider);

        let moveActive = false;

        function move(movement) {
          if (!moveActive) {
            return;
          }

          const relativeOffset = movement.endPosition.x;
          const splitPosition =
            (slider.offsetLeft + relativeOffset) / slider.parentElement.offsetWidth;
          slider.style.left = `${100.0 * splitPosition}%`;
          viewer.scene.splitPosition = splitPosition;
        }

        handler.setInputAction(function () {
          moveActive = true;
        }, Cesium.ScreenSpaceEventType.LEFT_DOWN);
        handler.setInputAction(function () {
          moveActive = true;
        }, Cesium.ScreenSpaceEventType.PINCH_START);

        handler.setInputAction(move, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
        handler.setInputAction(move, Cesium.ScreenSpaceEventType.PINCH_MOVE);

        handler.setInputAction(function () {
          moveActive = false;
        }, Cesium.ScreenSpaceEventType.LEFT_UP);
        handler.setInputAction(function () {
          moveActive = false;
        }, Cesium.ScreenSpaceEventType.PINCH_END);
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
