<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="CZML Colors" />
    <meta name="cesium-sandcastle-labels" content="CZML" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>

    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const czml = [
          {
            id: "document",
            name: "CZML Colors",
            version: "1.0",
          },
          {
            id: "rgba",
            name: "Rectangle with outline using RGBA Colors",
            rectangle: {
              coordinates: {
                wsenDegrees: [-120, 40, -110, 50],
              },
              fill: true,
              material: {
                solidColor: {
                  color: {
                    rgba: [255, 0, 0, 100],
                  },
                },
              },
              height: 0, // disables ground clamping, needed for outlines
              outline: true,
              outlineColor: {
                rgba: [0, 0, 0, 255],
              },
            },
          },
          {
            id: "rgbaf",
            name: "Rectangle using RGBAF Colors",
            rectangle: {
              coordinates: { wsenDegrees: [-100, 40, -90, 50] },
              fill: true,
              material: {
                solidColor: {
                  color: {
                    rgbaf: [1, 0, 0, 0.39],
                  },
                },
              },
              height: 0, // disables ground clamping, needed for outlines
              outline: true,
              outlineColor: {
                rgba: [0, 0, 0, 255],
              },
            },
          },
        ];

        const viewer = new Cesium.Viewer("cesiumContainer");
        const dataSourcePromise = Cesium.CzmlDataSource.load(czml);
        viewer.dataSources.add(dataSourcePromise);
        viewer.zoomTo(dataSourcePromise); //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
