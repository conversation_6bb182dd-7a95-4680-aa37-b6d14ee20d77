<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="Draw a circle or extruded circle on the globe surface."
    />
    <meta name="cesium-sandcastle-labels" content="Development" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        // Create the viewer.
        const viewer = new Cesium.Viewer("cesiumContainer");
        const scene = viewer.scene;

        // Example 1: Draw a red circle on the globe surface.

        // Create the circle geometry.
        let circleGeometry = new Cesium.CircleGeometry({
          center: Cesium.Cartesian3.fromDegrees(-95.0, 43.0),
          radius: 250000.0,
          vertexFormat: Cesium.PerInstanceColorAppearance.VERTEX_FORMAT,
        });
        // Create a geometry instance using the circle geometry
        // created above. We can also specify a color attribute,
        // in this case, we're creating a translucent red color.
        const redCircleInstance = new Cesium.GeometryInstance({
          geometry: circleGeometry,
          attributes: {
            color: Cesium.ColorGeometryInstanceAttribute.fromColor(
              new Cesium.Color(1.0, 0.0, 0.0, 0.5),
            ),
          },
        });
        // Add the geometry instance to primitives.
        scene.primitives.add(
          new Cesium.Primitive({
            geometryInstances: redCircleInstance,
            appearance: new Cesium.PerInstanceColorAppearance({
              closed: true,
            }),
          }),
        );

        // Example 2: Draw a green extruded circle.

        // Create the circle geometry.  To extrude, specify the
        // height of the geometry with the extrudedHeight option.
        circleGeometry = new Cesium.CircleGeometry({
          center: Cesium.Cartesian3.fromDegrees(-90.0, 50.0),
          radius: 250000.0,
          extrudedHeight: 300000.0,
          vertexFormat: Cesium.PerInstanceColorAppearance.VERTEX_FORMAT,
        });
        // Create a geometry instance using the circle geometry
        // created above. Set the color attribute to a
        // translucent green.
        const greenCircleInstance = new Cesium.GeometryInstance({
          geometry: circleGeometry,
          attributes: {
            color: Cesium.ColorGeometryInstanceAttribute.fromColor(
              new Cesium.Color(0.0, 1.0, 0.0, 0.5),
            ),
          },
        });
        // Add the geometry instance to primitives.
        scene.primitives.add(
          new Cesium.Primitive({
            geometryInstances: greenCircleInstance,
            appearance: new Cesium.PerInstanceColorAppearance({
              closed: true,
            }),
          }),
        );

        // Example 3: Draw a blue circle at a height.

        // Create the circle geometry.  Use the height option
        // to set the circles distance from the ground.
        circleGeometry = new Cesium.CircleGeometry({
          center: Cesium.Cartesian3.fromDegrees(-85.0, 40.0),
          radius: 400000.0,
          height: 150000.0,
          vertexFormat: Cesium.PerInstanceColorAppearance.VERTEX_FORMAT,
        });
        // Create a geometry instance using the circle geometry
        // created above. Set the color attribute to a solid blue.
        const blueCircleInstance = new Cesium.GeometryInstance({
          geometry: circleGeometry,
          attributes: {
            color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.BLUE),
          },
        });
        // Add the geometry instance to primitives.
        scene.primitives.add(
          new Cesium.Primitive({
            geometryInstances: blueCircleInstance,
            appearance: new Cesium.PerInstanceColorAppearance({
              closed: true,
              translucent: false,
            }),
          }),
        );
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
