﻿<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="Sandcastle-header.js"></script>
    <script type="text/javascript" src="Sandcastle-client.js"></script>
    <script type="text/javascript" src="ThirdParty/pako.min.js"></script>
    <script type="module" src="load-cesium-es6.js"></script>
    <script type="text/javascript" src="Sandcastle-helpers.js"></script>
    <style>
      .fullSize {
        display: none;
      }
      .sandcastle-loading::after {
        font: 24px sans-serif;
        content: "Loading...";
      }
    </style>
  </head>
  <body
    class="sandcastle-loading"
    data-sandcastle-bucket="bucket-requirejs.html"
  >
    <script type="text/javascript">
      /*global pako */
      let base64String;
      let data;

      // Since we are setting the HTML base href below, we need to get the getCesiumBaseUrl
      // upfront so that the value is correct. It gets cached for future calls.
      // This is only a problem when using combined Cesium, which is the only time Cesium
      // will be defined below.
      if (typeof Cesium !== "undefined") {
        Cesium.buildModuleUrl.getCesiumBaseUrl();
      }

      if (window.location.hash.indexOf("#c=") === 0) {
        base64String = window.location.hash.substr(3);
        data = window.decodeBase64Data(base64String, pako);
        //Handle case where demo is in a sub-directory by modifying
        //the HTML to add a base href.
        document.head.innerHTML = `${document.head.innerHTML}<base href="${data.baseHref}" />`;
        window.sandcastleData = data;
      }

      let code;
      if (window.sandcastleData) {
        base64String = window.location.hash.substr(3);
        data = window.sandcastleData;
        let html = data.html;
        code = data.code;
        const isIE11 = !!window.MSInputMethodContext && !!document.documentMode;
        if (isIE11) {
          html = html.replace("../templates", "templates");
        }

        // Add the HTML content
        const htmlElement = document.createElement("div");
        htmlElement.innerHTML = html;
        document.body.appendChild(htmlElement);

        // Add the JavaScript
        const scriptElement = document.createElement("script");
        const isFirefox = navigator.userAgent.indexOf("Firefox/") >= 0;
        document.head.appendChild(scriptElement);
        scriptElement.innerHTML = window.embedInSandcastleTemplate(
          code,
          isFirefox,
        );
      }
    </script>
  </body>
</html>
