<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="Use Viewer to start building new applications or easily embed Cesium into existing applications."
    />
    <meta name="cesium-sandcastle-labels" content="Beginner, Showcases" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer", {
          shouldAnimate: true,
        });

        function dataCallback(interval, index) {
          let time;
          if (index === 0) {
            // leading
            time = Cesium.JulianDate.toIso8601(interval.stop);
          } else {
            time = Cesium.JulianDate.toIso8601(interval.start);
          }

          return {
            Time: time,
          };
        }

        const times = Cesium.TimeIntervalCollection.fromIso8601({
          iso8601: "2015-07-30/2017-06-16/P1D",
          leadingInterval: true,
          trailingInterval: true,
          isStopIncluded: false, // We want stop time to be part of the trailing interval
          dataCallback: dataCallback,
        });

        // Add a WMTS imagery layer.
        // This comes from NASA's GIBS API.
        // See https://wiki.earthdata.nasa.gov/display/GIBS/GIBS+API+for+Developers#GIBSAPIforDevelopers-OGCWebMapService(WMS)
        const provider = new Cesium.WebMapTileServiceImageryProvider({
          url: "https://gibs.earthdata.nasa.gov/wmts/epsg4326/best/MODIS_Terra_CorrectedReflectance_TrueColor/default/{Time}/{TileMatrixSet}/{TileMatrix}/{TileRow}/{TileCol}.jpg",
          layer: "MODIS_Terra_CorrectedReflectance_TrueColor",
          style: "default",
          tileMatrixSetID: "250m",
          maximumLevel: 5,
          format: "image/jpeg",
          clock: viewer.clock,
          times: times,
          credit: "NASA Global Imagery Browse Services for EOSDIS",
        });
        const layer = new Cesium.ImageryLayer(provider);

        // Make the weather layer semi-transparent to see the underlying geography.
        layer.alpha = 0.5;

        viewer.imageryLayers.add(layer);

        const start = Cesium.JulianDate.fromIso8601("2015-07-30");
        const stop = Cesium.JulianDate.fromIso8601("2017-06-17");

        viewer.timeline.zoomTo(start, stop);

        const clock = viewer.clock;
        clock.startTime = start;
        clock.stopTime = stop;
        clock.currentTime = start;
        clock.clockRange = Cesium.ClockRange.LOOP_STOP;
        clock.multiplier = 7200;
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
