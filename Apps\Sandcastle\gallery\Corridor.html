<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="Draw a corridor." />
    <meta name="cesium-sandcastle-labels" content="Geometries" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer");

        const redCorridor = viewer.entities.add({
          name: "Red corridor on surface with rounded corners",
          corridor: {
            positions: Cesium.Cartesian3.fromDegreesArray([
              -100.0, 40.0, -105.0, 40.0, -105.0, 35.0,
            ]),
            width: 200000.0,
            material: Cesium.Color.RED.withAlpha(0.5),
          },
        });

        const greenCorridor = viewer.entities.add({
          name: "Green corridor at height with mitered corners and outline",
          corridor: {
            positions: Cesium.Cartesian3.fromDegreesArray([
              -90.0, 40.0, -95.0, 40.0, -95.0, 35.0,
            ]),
            height: 100000.0,
            width: 200000.0,
            cornerType: Cesium.CornerType.MITERED,
            material: Cesium.Color.GREEN,
            outline: true, // height required for outlines to display
          },
        });

        const blueCorridor = viewer.entities.add({
          name: "Blue extruded corridor with beveled corners and outline",
          corridor: {
            positions: Cesium.Cartesian3.fromDegreesArray([
              -80.0, 40.0, -85.0, 40.0, -85.0, 35.0,
            ]),
            height: 200000.0,
            extrudedHeight: 100000.0,
            width: 200000.0,
            cornerType: Cesium.CornerType.BEVELED,
            material: Cesium.Color.BLUE.withAlpha(0.5),
            outline: true, // height or extrudedHeight must be set for outlines to display
            outlineColor: Cesium.Color.WHITE,
          },
        });

        viewer.zoomTo(viewer.entities);
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
