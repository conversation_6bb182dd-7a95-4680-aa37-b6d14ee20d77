/* RadioButton
 * 
 * Styling RadioButton mainly includes:
 * 
 * 1. Containers
 * 		.dijitRadio|.dijitRadioIcon - for border, padding, width|height and background image
 * 
 * 2. RadioButton within ToggleButton
 * 		.dijitToggleButton|.dijitToggleButtonChecked .* - for background image
 * 
 * 3. Checked state
 * 		.dijitRadioChecked - for checked background-color|image
 * 		.dijitToggleButtonChecked - for border, background-color|image, display and width|height
 * 
 * 4. Hover state
 * 		.dijitRadioHover|.dijitRadioCheckedHover - for background image
 * 
 * 5. Disabled state
 * 		.dijitRadioDisabled|.dijitRadioCheckedDisabled - for background image
 */

@import "../variables";

.claro .dijitToggleButton .dijitRadio,
.claro .dijitToggleButton .dijitRadioIcon {
	background-image: url("../@{image-form-checkbox-and-radios}");
}

.dj_ie6 .claro .dijitToggleButton .dijitRadio,
.dj_ie6 .claro .dijitToggleButton .dijitRadioIcon {
	background-image: url("../@{image-form-checkbox-and-radios-ie6}");
}

.claro .dijitRadio,
.claro .dijitRadioIcon	{		/* inside a toggle button */
	background-image: url("../@{image-form-checkbox-and-radios}"); /* checkbox sprite image */
	background-repeat: no-repeat;
	width: 15px;
	height: 15px;
	margin: 0 2px 0 0;
	padding: 0;
}

.dj_ie6 .claro .dijitRadio,
.dj_ie6 .claro .dijitRadioIcon	{		/* inside a toggle button */
	background-image: url("../@{image-form-checkbox-and-radios-ie6}"); /* checkbox sprite image */
}

.claro .dijitRadio{
	/* unselected */
	background-position: -105px;
}

.claro .dijitToggleButton .dijitRadioIcon {
	/* unselected */
	background-position: -107px;
}

.claro .dijitRadioDisabled {
	/* unselected and disabled */
	background-position: -165px;
}

.claro .dijitRadioHover {
	/* hovering over an unselected enabled radio button */
	background-position: -135px;
}

.claro .dijitRadioChecked{
	background-position: -90px;
}

.claro .dijitToggleButtonChecked .dijitRadioIcon {
	background-position: -92px;
}

.claro .dijitRadioCheckedHover{
	background-position: -120px;
}

.claro .dijitRadioCheckedDisabled {
	/* selected but disabled */
	background-position: -150px;
}
