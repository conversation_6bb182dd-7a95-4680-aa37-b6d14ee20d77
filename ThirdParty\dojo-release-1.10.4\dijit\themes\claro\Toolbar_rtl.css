/* Toolbar RTL */
/* Repeated rules from Toolbar.css to override rule from Button_rtl.css, which is loaded after Toolbar.css */
.claro .dijitToolbar .dijitComboButtonRtl .dijitButtonNode {
  border-width: 0;
  padding: 2px;
}
.claro .dijitToolbar .dijitComboButtonRtlHover .dijitButtonNode,
.claro .dijitToolbar .dijitComboButtonRtlActive .dijitButtonNode {
  border-width: 1px;
  padding: 1px;
}
.claro .dijitToolbar .dijitComboButtonRtl .dijitStretch {
  /* no rounded border on side adjacent to arrow */

  -moz-border-radius: 0 2px 2px 0;
  border-radius: 0 2px 2px 0;
}
.claro .dijitToolbar .dijitComboButtonRtl .dijitArrowButton {
  /* no rounded border on side adjacent to button */

  -moz-border-radius: 2px 0 0 2px;
  border-radius: 2px 0 0 2px;
}
.claro .dijitToolbar .dijitComboButtonRtlHover .dijitArrowButton,
.claro .dijitToolbar .dijitComboButtonRtlActive .dijitArrowButton {
  /* border between button and arrow */

  border-left-width: 1px;
  border-right-width: 0;
  padding-left: 1px;
  padding-right: 2px;
}
