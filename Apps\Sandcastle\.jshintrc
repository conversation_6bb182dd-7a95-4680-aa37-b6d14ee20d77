{"esversion": 11, "predef": ["JSON", "require", "console", "Sandcastle", "Cesium"], "bitwise": false, "camelcase": false, "curly": true, "eqeqeq": true, "forin": true, "freeze": true, "immed": true, "latedef": false, "newcap": true, "noarg": true, "noempty": true, "nonbsp": true, "nonew": true, "plusplus": false, "quotmark": false, "undef": true, "unused": false, "strict": true, "asi": false, "boss": false, "debug": false, "eqnull": false, "moz": false, "evil": false, "expr": false, "funcscope": false, "globalstrict": false, "iterator": false, "lastsemic": false, "laxbreak": false, "laxcomma": false, "loopfunc": false, "multistr": true, "noyield": false, "notypeof": false, "proto": false, "scripturl": false, "shadow": false, "sub": false, "supernew": false, "validthis": false, "browser": true, "browserify": false, "couch": false, "devel": true, "dojo": false, "jasmine": false, "jquery": false, "mocha": true, "mootools": false, "node": false, "nonstandard": false, "prototypejs": false, "qunit": false, "rhino": false, "shelljs": false, "worker": false, "wsh": false, "yui": false}