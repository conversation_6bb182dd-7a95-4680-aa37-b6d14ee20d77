<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />
    <!-- Use Chrome Frame in IE -->
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="Sample photogrammetry and classification datasets both rendered with 3D Tiles."
    />
    <meta name="cesium-sandcastle-labels" content="Showcases, 3D Tiles" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        // An example of using a b3dm tileset to classify another b3dm tileset.
        const viewer = new Cesium.Viewer("cesiumContainer", {
          terrain: Cesium.Terrain.fromWorldTerrain(),
        });

        try {
          // A normal b3dm tileset containing photogrammetry
          const tileset = await Cesium.Cesium3DTileset.fromIonAssetId(40866);
          viewer.scene.primitives.add(tileset);
          viewer.zoomTo(tileset);

          const classificationTilesetUrl =
            "../../SampleData/Cesium3DTiles/Classification/Photogrammetry/tileset.json";
          // A b3dm tileset used to classify the photogrammetry tileset
          const classificationTileset = await Cesium.Cesium3DTileset.fromUrl(
            classificationTilesetUrl,
            {
              classificationType: Cesium.ClassificationType.CESIUM_3D_TILE,
            },
          );
          classificationTileset.style = new Cesium.Cesium3DTileStyle({
            color: "rgba(255, 0, 0, 0.5)",
          });
          viewer.scene.primitives.add(classificationTileset);

          // The same b3dm tileset used for classification, but rendered normally for comparison.
          const nonClassificationTileset = await Cesium.Cesium3DTileset.fromUrl(
            classificationTilesetUrl,
            {
              show: false,
            },
          );
          nonClassificationTileset.style = new Cesium.Cesium3DTileStyle({
            color: "rgba(255, 0, 0, 0.5)",
          });
          viewer.scene.primitives.add(nonClassificationTileset);

          Sandcastle.addToggleButton("Show classification", true, function (checked) {
            classificationTileset.show = checked;
            nonClassificationTileset.show = !checked;
          });
        } catch (error) {
          console.log(`Error loading tileset: ${error}`);
        } //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
