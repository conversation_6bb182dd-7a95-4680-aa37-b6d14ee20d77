/* Accordion
 * 
 * Styling AccordionContainer basically means styling the accordion pane (dijitAccordionInnerContainer)
 * and the title inside of it (dijitAccordionTitle).   There are 4 basic states to style:
 * 
 * 1. closed pane (and default styling): 
 * 		.dijitAccordionInnerContainer - container for each accordion child
 * 		.dijitAccordionTitle - title for each accordion child
 *
 * 2. active closed pane (ie, mouse down on a title bar)
 * 		.dijitAccordionInnerContainerActive - for background-color, border
 * 		.dijitAccordionInnerContainerActive dijitAccordionTitle - for text color
 * 
 * 3. open pane (expanded child)
 *		.dijitAccordionChildWrapper - wraps around the child widget (typically ContentPane)
 *			setting a margin so that there's blue trim all the way around the child
 *
 * 		These rules need to override the closed pane active:
 *
 * 		.dijitAccordionInnerContainerSelected - for background-color, border
 * 		.dijitAccordionInnerContainerSelected .dijitAccordionTitle - for text color
 * 
 * 4. hovered pane, open or closed
 * 		The selectors below affect hovering over both a closed pane (ie, hovering a title bar),
 * 		and hovering over an open pane.   Also, treat mouse down on an open pane as a hover:
 *
 * 		.dijitAccordionInnerContainerHover, .dijitAccordionInnerContainerSelectedActive - for background-color, border
 * 		.dijitAccordionInnerContainerHover .dijitAccordionTitle - for text color
 */

@import "../variables";

.claro .dijitAccordionContainer {
	border:none;
}
.claro .dijitAccordionInnerContainer {
	background-color: @unselected-background-color;	/* gray, for closed pane */
	border:solid 1px @border-color;
	margin-bottom:1px;
	.transition-property(background-color,border);
 	.transition-duration(.3s);
	.transition-timing-function(linear);
}
.claro .dijitAccordionTitle {
	background-color: transparent;	/* pick up color from dijitAccordionInnerContainer */
	.standard-gradient("../");
	padding: 5px 7px 2px 7px;
	min-height:17px;
	color:@unselected-text-color;
}

.claro .dijitAccordionContainer .dijitAccordionChildWrapper {
	/* this extends the blue trim styling of the title bar to wrapping around the node.
	 * done by setting margin
	 */
	background-color:@pane-background-color;
	border:1px solid @selected-border-color;
	margin: 0 2px 2px;
}
	
.claro .dijitAccordionContainer .dijitAccordionContainer-child {
	/* this is affecting the child widget itself */
	padding: 9px;
}

/* Hover state for closed pane */

.claro .dijitAccordionInnerContainerHover {
	border:1px solid @hovered-border-color;
	background-color:@hovered-background-color;
	.transition-duration(.2s);
}

.claro .dijitAccordionInnerContainerHover .dijitAccordionTitle {
	color:@hovered-text-color;
}

/* Active state for closed pane */

.claro .dijitAccordionInnerContainerActive {
	border:1px solid @selected-border-color;
	background-color:@pressed-background-color;
	.transition-duration(.1s);
}
.claro .dijitAccordionInnerContainerActive .dijitAccordionTitle {
	.active-gradient("../");
	color:@selected-text-color;
}

/* Open (a.k.a. selected) pane */

.claro .dijitAccordionInnerContainerSelected {
	border-color:@selected-border-color;
	background-color: @selected-background-color;
}
.claro .dijitAccordionInnerContainerSelected .dijitAccordionTitle {
	color:@selected-text-color;
	.standard-gradient("../");	/* avoid effect when clicking the title of the open pane */
}

