<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="Create and display map pins." />
    <meta name="cesium-sandcastle-labels" content="Beginner" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer", {
          timeline: false,
          animation: false,
        });

        const pinBuilder = new Cesium.PinBuilder();

        const bluePin = viewer.entities.add({
          name: "Blank blue pin",
          position: Cesium.Cartesian3.fromDegrees(-75.170726, 39.9208667),
          billboard: {
            image: pinBuilder.fromColor(Cesium.Color.ROYALBLUE, 48).toDataURL(),
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          },
        });

        const questionPin = viewer.entities.add({
          name: "Question mark",
          position: Cesium.Cartesian3.fromDegrees(-75.1698529, 39.9220071),
          billboard: {
            image: pinBuilder.fromText("?", Cesium.Color.BLACK, 48).toDataURL(),
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          },
        });

        const url = Cesium.buildModuleUrl("Assets/Textures/maki/grocery.png");
        const groceryPin = Promise.resolve(
          pinBuilder.fromUrl(url, Cesium.Color.GREEN, 48),
        ).then(function (canvas) {
          return viewer.entities.add({
            name: "Grocery store",
            position: Cesium.Cartesian3.fromDegrees(-75.1705217, 39.921786),
            billboard: {
              image: canvas.toDataURL(),
              verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            },
          });
        });

        //Create a red pin representing a hospital from the maki icon set.
        const hospitalPin = Promise.resolve(
          pinBuilder.fromMakiIconId("hospital", Cesium.Color.RED, 48),
        ).then(function (canvas) {
          return viewer.entities.add({
            name: "Hospital",
            position: Cesium.Cartesian3.fromDegrees(-75.1698606, 39.9211275),
            billboard: {
              image: canvas.toDataURL(),
              verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            },
          });
        });

        //Since some of the pins are created asynchronously, wait for them all to load before zooming/
        Promise.all([bluePin, questionPin, groceryPin, hospitalPin]).then(
          function (pins) {
            viewer.zoomTo(pins);
          },
        );

        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
