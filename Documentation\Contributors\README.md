# Contributor Guides

- [CONTRIBUTING.md](../../CONTRIBUTING.md) - Start here. How to find something to work on, submit issues, and open pull requests.
- [Build Guide](BuildGuide/README.md) - How to build and run CesiumJS locally.
- [Continuous Integration Guide](ContinuousIntegration/README.md) - How we use GitHub Actions for continuous integration (CI) and continuous deployment (CD).
- **IDEs** - use any IDE you want for CesiumJS development. Most contributors use WebStorm (commercial) or VSCode (open source).
  - [VSCode Guide](VSCodeGuide/README.md) - How to set up VSCode.
- [Coding Guide](CodingGuide/README.md) - JavaScript and GLSL coding conventions and best practices for design, maintainability, and performance.
- [Testing Guide](TestingGuide/README.md) - How to run the CesiumJS tests and write awesome tests.
- [Performance Testing Guide](PerformanceTestingGuide/README.md) - Best practices for measuring runtime performance.
- [Documentation Guide](DocumentationGuide/README.md) - How to write great reference documentation.
- [Code Review Guide](CodeReviewGuide/README.md) - Best practices for reviewing code in pull requests.
- [Presenter's Guide](PresentersGuide/README.md) - Tips for giving talks.
- [Committer's Guide](CommittersGuide/README.md) - What to do with commit access to the main CesiumJS repo.
- [Release Guide](ReleaseGuide/README.md) - How to ship a CesiumJS release.
  - [Release Schedule](ReleaseGuide//ReleaseSchedule.md) - The upcoming monthly release schedule and the developer responsible for managing each release
  - [Prelease Guide](ReleaseGuide/Prereleases/README.md) - If and how to publish a prerelease version ahead of the typical monthly release
