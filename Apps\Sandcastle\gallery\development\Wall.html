<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="Draw a wall on the globe surface." />
    <meta name="cesium-sandcastle-labels" content="Development" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        // Create the viewer.
        const viewer = new Cesium.Viewer("cesiumContainer");
        const scene = viewer.scene;

        // Example 1: Draw a red wall with constant min and max heights

        // Use the maximumHeight and minumumHeight options to specify
        // the heights of the top and bottom of the wall.
        const redWallInstance = new Cesium.GeometryInstance({
          geometry: Cesium.WallGeometry.fromConstantHeights({
            positions: Cesium.Cartesian3.fromDegreesArray([-115.0, 44.0, -90.0, 44.0]),
            maximumHeight: 200000.0,
            minimumHeight: 100000.0,
            vertexFormat: Cesium.PerInstanceColorAppearance.VERTEX_FORMAT,
          }),
          attributes: {
            color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.RED),
          },
        });

        // Example 2: Draw a green wall with constant height

        // If minumumHeight is not specified, the wall will be
        // drawn on the globe surface.
        const greenWallInstance = new Cesium.GeometryInstance({
          geometry: Cesium.WallGeometry.fromConstantHeights({
            positions: Cesium.Cartesian3.fromDegreesArray([
              -107.0, 43.0, -97.0, 43.0, -97.0, 40.0, -107.0, 40.0, -107.0, 43.0,
            ]),
            maximumHeight: 100000.0,
            vertexFormat: Cesium.PerInstanceColorAppearance.VERTEX_FORMAT,
          }),
          attributes: {
            color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.GREEN),
          },
        });

        // Example 3: Draw a blue wall with per position heights
        // To use per position heights, create an array of heights
        // for maximumHeights (and optionally minumumHeights)
        // with a length equal to the number of positions.
        const positions = Cesium.Cartesian3.fromDegreesArray([
          -115.0, 50.0, -112.5, 50.0, -110.0, 50.0, -107.5, 50.0, -105.0, 50.0, -102.5,
          50.0, -100.0, 50.0, -97.5, 50.0, -95.0, 50.0, -92.5, 50.0, -90.0, 50.0,
        ]);
        const maximumHeights = [
          100000, 200000, 100000, 200000, 100000, 200000, 100000, 200000, 100000, 200000,
          100000,
        ];
        const minimumHeights = [0, 100000, 0, 100000, 0, 100000, 0, 100000, 0, 100000, 0];

        const blueWallInstance = new Cesium.GeometryInstance({
          geometry: new Cesium.WallGeometry({
            positions: positions,
            maximumHeights: maximumHeights,
            minimumHeights: minimumHeights,
            vertexFormat: Cesium.PerInstanceColorAppearance.VERTEX_FORMAT,
          }),
          attributes: {
            color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.BLUE),
          },
        });

        // Add all wall instances to primitives.
        scene.primitives.add(
          new Cesium.Primitive({
            geometryInstances: [blueWallInstance, redWallInstance, greenWallInstance],
            appearance: new Cesium.PerInstanceColorAppearance({
              translucent: false,
            }),
          }),
        );
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
