<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="Draw a cylinder or cone." />
    <meta name="cesium-sandcastle-labels" content="Development" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        // Create the viewer.
        const viewer = new Cesium.Viewer("cesiumContainer");
        const scene = viewer.scene;

        // Example 1: Draw a green cylinder and position with
        // a model matrix.

        // Cylinder geometries are initially centered on the origin.
        // We can use a model matrix to position the cylinder on the
        // globe surface.
        const length = 400000.0;
        let positionOnEllipsoid = Cesium.Cartesian3.fromDegrees(-100.0, 40.0);
        let modelMatrix = Cesium.Matrix4.multiplyByTranslation(
          Cesium.Transforms.eastNorthUpToFixedFrame(positionOnEllipsoid),
          new Cesium.Cartesian3(0.0, 0.0, length * 0.5),
          new Cesium.Matrix4(),
        );
        // Create the cylinder geometry.
        let cylinderGeometry = new Cesium.CylinderGeometry({
          length: length,
          topRadius: 200000.0,
          bottomRadius: 200000.0,
          vertexFormat: Cesium.PerInstanceColorAppearance.VERTEX_FORMAT,
        });
        // Create a geometry instance using the cylinder geometry
        // created above. We can also specify a color attribute,
        // in this case, we're creating a solid green color.
        const greenCylinder = new Cesium.GeometryInstance({
          geometry: cylinderGeometry,
          modelMatrix: modelMatrix,
          attributes: {
            color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.GREEN),
          },
        });

        // Example 2: Draw a red cone and position with
        // a model matrix.
        positionOnEllipsoid = Cesium.Cartesian3.fromDegrees(-105.0, 40.0);
        modelMatrix = Cesium.Matrix4.multiplyByTranslation(
          Cesium.Transforms.eastNorthUpToFixedFrame(positionOnEllipsoid),
          new Cesium.Cartesian3(0.0, 0.0, length * 0.5),
          new Cesium.Matrix4(),
        );
        // Create the cylinder geometry.  To create a cone, set the
        // top radius to zero.
        cylinderGeometry = new Cesium.CylinderGeometry({
          length: length,
          topRadius: 0.0,
          bottomRadius: 200000.0,
          vertexFormat: Cesium.PerInstanceColorAppearance.VERTEX_FORMAT,
        });
        // Create a geometry instance using the cylinder geometry
        // created above.
        const redCone = new Cesium.GeometryInstance({
          geometry: cylinderGeometry,
          modelMatrix: modelMatrix,
          attributes: {
            color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.RED),
          },
        });

        // Add both instances to primitives.
        scene.primitives.add(
          new Cesium.Primitive({
            geometryInstances: [greenCylinder, redCone],
            appearance: new Cesium.PerInstanceColorAppearance({
              closed: true,
              translucent: false,
            }),
          }),
        );
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
