/* Menu 

There are three areas of styling for the Menu:  
 
 1. The menu 
 	There are three types of menus:
 	i) Context Menu
 	ii) Drop down Menu
 	iii) Navigation Menu
 	All three types of menus are affected by the .dijitMenu class in which you can set the background-color, padding and border
 	.dijitMenu affects the drop down menu in TimeTextBox, Calendar, ComboBox and FilteringSelect
  .dijitMenuTable - for padding - also affects Select widget 	

 2. The menu bar
 	.dijitMenuBar - for border, margins, padding, background-color of the menu bar
 	.dijitMenuBar .dijitMenuItem - for padding, text color of menu items in the menu bar (overrides .dijitMenuItem) 
 	
 3. Menu items - items in the menu.  
 	.dijitMenuItem - for color
 	.dijitMenuItemHover, .dijitMenuItemSelected - for background-color, border, text color, padding of a menu item or menubar item that has been hovered over or selected	
 	.dijitMenuItemActive - for background-color of an active (mousedown) menu item
	td.dijitMenuItemIconCell - for padding around a  menu item's icon
	td.dijitMenuItemLabel - for padding around a menu item's label	
	.dijitMenuSeparatorTop - for border, top border, of the separator
	.dijitMenuSeparatorBottom - for bottom margin of the separator
	
	Styles specific to ComboBox and FilteringSelect widgets: 
	.dijitComboBoxMenu .dijitMenuItem - for padding and border of a menu item in a ComboBox or FilteringSelect widget's menu
	.dijitComboBoxMenu .dijitMenuItemSelected- for text color, background-color and border of a menu item in a ComboBox or FilteringSelect widget's menu

*/
.claro .dijitMenuBar {
  border: 1px solid #b5bcc7;
  margin: 0;
  padding: 0;
  background-color: #efefef;
  background-image: url("images/standardGradient.png");
  background-repeat: repeat-x;
  background-image: -moz-linear-gradient(rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0) 100%);
  background-image: -webkit-linear-gradient(rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0) 100%);
  background-image: -o-linear-gradient(rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0) 100%);
  background-image: linear-gradient(rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0) 100%);
  _background-image: none;
}
.claro .dijitMenu {
  background-color: #ffffff;
  border: 1px solid #759dc0;
}
.claro .dijitMenuItem {
  color: #000000;
}
.claro .dijitMenuBar .dijitMenuItem {
  padding: 6px 10px 7px;
  margin: -1px;
}
.claro .dijitMenuBar .dijitMenuItemHover,
.claro .dijitMenuBar .dijitMenuItemSelected {
  border: solid 1px #759dc0;
  padding: 5px 9px 6px;
}
/* this prevents jiggling upon hover of a menu item */
.claro .dijitMenuTable {
  border-collapse: separate;
  border-spacing: 0 0;
  padding: 0;
}
.claro .dijitMenu .dijitMenuItem td,
.claro .dijitComboBoxMenu .dijitMenuItem {
  padding: 2px;
  border-width: 1px 0 1px 0;
  border-style: solid;
  border-color: #ffffff;
}
/* hover over a MenuItem or MenuBarItem */
.claro .dijitMenu .dijitMenuItemHover td,
.claro .dijitMenu .dijitMenuItemSelected td,
.claro .dijitMenuItemHover,
.claro .dijitComboBoxMenu .dijitMenuItemHover,
.claro .dijitMenuItemSelected {
  border-color: #759dc0;
  background-color: #abd6ff;
  background-image: url("images/standardGradient.png");
  background-repeat: repeat-x;
  background-image: -moz-linear-gradient(rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0) 100%);
  background-image: -webkit-linear-gradient(rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0) 100%);
  background-image: -o-linear-gradient(rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0) 100%);
  background-image: linear-gradient(rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0) 100%);
  _background-image: none;
}
.claro .dijitMenuItemActive {
  background-image: url("images/activeGradient.png");
  background-repeat: repeat-x;
  background-image: -moz-linear-gradient(rgba(190, 190, 190, 0.98) 0px, rgba(255, 255, 255, 0.65) 3px, rgba(255, 255, 255, 0) 100%);
  background-image: -webkit-linear-gradient(rgba(190, 190, 190, 0.98) 0px, rgba(255, 255, 255, 0.65) 3px, rgba(255, 255, 255, 0) 100%);
  background-image: -o-linear-gradient(rgba(190, 190, 190, 0.98) 0px, rgba(255, 255, 255, 0.65) 3px, rgba(255, 255, 255, 0) 100%);
  background-image: linear-gradient(rgba(190, 190, 190, 0.98) 0px, rgba(255, 255, 255, 0.65) 3px, rgba(255, 255, 255, 0) 100%);
  _background-image: none;
}
.dj_ie .claro .dijitMenuActive .dijitMenuItemHover,
.dj_ie .claro .dijitMenuActive .dijitMenuItemSelected,
.dj_ie .claro .dijitMenuPassive .dijitMenuItemHover,
.dj_ie .claro .dijitMenuPassive .dijitMenuItemSelected {
  padding-top: 6px;
  padding-bottom: 5px;
  margin-top: -3px;
}
.claro td.dijitMenuItemIconCell {
  padding: 2px;
  margin: 0 0 0 4px;
}
.claro td.dijitMenuItemLabel {
  padding-top: 5px;
  padding-bottom: 5px;
}
.claro .dijitMenuExpand {
  width: 7px;
  height: 7px;
  background-image: url("images/spriteArrows.png");
  background-position: -14px 0;
  margin-right: 3px;
  margin-bottom: 4px;
}
.claro .dijitMenuSeparatorTop {
  height: auto;
  margin-top: 1px;
  /* prevents spacing above/below separator */

  border-bottom: 1px solid #b5bcc7;
}
.claro .dijitMenuSeparatorBottom {
  height: auto;
  margin-bottom: 1px;
}
/* the checked menu item */
.claro .dijitCheckedMenuItem .dijitMenuItemIcon,
.claro .dijitRadioMenuItem .dijitMenuItemIcon {
  background-image: url("form/images/checkboxRadioButtonStates.png");
  background-repeat: no-repeat;
  background-position: -15px 50%;
  /* unchecked checkbox */

  width: 15px;
  height: 16px;
}
.dj_ie6 .claro .dijitCheckedMenuItem .dijitMenuItemIcon,
.dj_ie6 .claro .dijitRadioMenuItem .dijitMenuItemIcon {
  background-image: url("form/images/checkboxAndRadioButtons_IE6.png");
}
.claro .dijitCheckedMenuItemChecked .dijitCheckedMenuItemIcon {
  background-position: 0 50%;
  /* checked checkbox */

}
.claro .dijitRadioMenuItem .dijitMenuItemIcon {
  background-position: -105px 50%;
  /* unfilled circle */

}
.claro .dijitRadioMenuItemChecked .dijitMenuItemIcon {
  background-position: -90px 50%;
  /* filled circle */

}
/*ComboBox Menu*/
.claro .dijitComboBoxMenu {
  margin-left: 0;
  background-image: none;
}
.claro .dijitMenu .dijitMenuItemSelected td,
.claro .dijitComboBoxMenu .dijitMenuItemSelected {
  color: #000000;
  border-color: #759dc0;
  background-color: #abd6ff;
}
.claro .dijitComboBoxMenuActive .dijitMenuItemSelected {
  background-color: #7dbdfa;
  /* TODO: why is this a different color than normal .dijitMenuItemSelected? */

}
.claro .dijitMenuPreviousButton,
.claro .dijitMenuNextButton {
  font-style: italic;
}
