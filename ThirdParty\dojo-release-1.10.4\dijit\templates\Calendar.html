<table cellspacing="0" cellpadding="0" class="dijitCalendarContainer" role="grid" aria-labelledby="${id}_mddb ${id}_year" data-dojo-attach-point="gridNode">
	<thead>
		<tr class="dijitReset dijitCalendarMonthContainer" valign="top">
			<th class='dijitReset dijitCalendarArrow' data-dojo-attach-point="decrementMonth" scope="col">
				<span class="dijitInline dijitCalendarIncrementControl dijitCalendarDecrease" role="presentation"></span>
				<span data-dojo-attach-point="decreaseArrowNode" class="dijitA11ySideArrow">-</span>
			</th>
			<th class='dijitReset' colspan="5" scope="col">
				<div data-dojo-attach-point="monthNode">
				</div>
			</th>
			<th class='dijitReset dijitCalendarArrow' scope="col" data-dojo-attach-point="incrementMonth">
				<span class="dijitInline dijitCalendarIncrementControl dijitCalendarIncrease" role="presentation"></span>
				<span data-dojo-attach-point="increaseArrowNode" class="dijitA11ySideArrow">+</span>
			</th>
		</tr>
		<tr role="row">
			${!dayCellsHtml}
		</tr>
	</thead>
	<tbody data-dojo-attach-point="dateRowsNode" data-dojo-attach-event="ondijitclick: _onDayClick" class="dijitReset dijitCalendarBodyContainer">
			${!dateRowsHtml}
	</tbody>
	<tfoot class="dijitReset dijitCalendarYearContainer">
		<tr>
			<td class='dijitReset' valign="top" colspan="7" role="presentation">
				<div class="dijitCalendarYearLabel">
					<span data-dojo-attach-point="previousYearLabelNode" class="dijitInline dijitCalendarPreviousYear" role="button"></span>
					<span data-dojo-attach-point="currentYearLabelNode" class="dijitInline dijitCalendarSelectedYear" role="button" id="${id}_year"></span>
					<span data-dojo-attach-point="nextYearLabelNode" class="dijitInline dijitCalendarNextYear" role="button"></span>
				</div>
			</td>
		</tr>
	</tfoot>
</table>
