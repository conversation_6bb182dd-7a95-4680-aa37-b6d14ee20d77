<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="Draw the outline of a circle." />
    <meta name="cesium-sandcastle-labels" content="Development" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        // Create the viewer.
        const viewer = new Cesium.Viewer("cesiumContainer");
        const scene = viewer.scene;

        // Example 1: Draw a circle outline on the globe surface.
        // Create the circle outline geometry.
        let circleOutlineGeometry = new Cesium.CircleOutlineGeometry({
          center: Cesium.Cartesian3.fromDegrees(-100.0, 40.0),
          radius: 200000.0,
        });
        // Create a geometry instance using the circle outline
        // created above.
        let circleOutlineInstance = new Cesium.GeometryInstance({
          geometry: circleOutlineGeometry,
          attributes: {
            color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.WHITE),
          },
        });
        // Add the geometry instance to primitives.
        scene.primitives.add(
          new Cesium.Primitive({
            geometryInstances: circleOutlineInstance,
            appearance: new Cesium.PerInstanceColorAppearance({
              flat: true,
              renderState: {
                lineWidth: Math.min(2.0, scene.maximumAliasedLineWidth),
              },
            }),
          }),
        );

        // Example 2: Draw a green extruded circle.
        // Create the circle outline geometry.  To extrude, specify
        // the height of the geometry with the extrudedHeight option.
        // The numberOfVerticalLines option is used to determine
        // how many lines connect the top and bottom of the circle.
        circleOutlineGeometry = new Cesium.CircleOutlineGeometry({
          center: Cesium.Cartesian3.fromDegrees(-95.0, 40.0),
          radius: 100000.0,
          extrudedHeight: 200000.0,
          numberOfVerticalLines: 16,
        });
        // Create a geometry instance using the circle outline
        // created above.
        circleOutlineInstance = new Cesium.GeometryInstance({
          geometry: circleOutlineGeometry,
          attributes: {
            color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.WHITE),
          },
        });
        // Add the geometry instance to primitives.
        scene.primitives.add(
          new Cesium.Primitive({
            geometryInstances: circleOutlineInstance,
            appearance: new Cesium.PerInstanceColorAppearance({
              flat: true,
              renderState: {
                lineWidth: Math.min(2.0, scene.maximumAliasedLineWidth),
              },
            }),
          }),
        );
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
