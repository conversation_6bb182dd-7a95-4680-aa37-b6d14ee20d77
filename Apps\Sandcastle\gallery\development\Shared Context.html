<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="Multiple views synced across time and space." />
    <meta name="cesium-sandcastle-labels" content="Beginner, Showcases,New in 1.129" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
      #cesiumContainer {
        display: flex;
        width: 100%;
        height: 100%;
      }
      #view3D {
        display: inline-block;
        width: 100%;
      }
      #view2D {
        display: inline-block;
        width: 100%;
      }
    </style>
    <div id="cesiumContainer" class="fullSize">
      <div id="view3D"></div>
      <div id="view2D"></div>
    </div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const contextOptions = new Cesium.SharedContext();
        // const contextOptions = undefined;
        // Uncomment the line above and comment out the one preceding it to illustrate how primitives cannot be shared between scenes by default.

        const options = {
          contextOptions,
          fullscreenButton: false,
          sceneModePicker: false,
        };

        const view1 = new Cesium.Viewer("view3D", options);
        const view2 = new Cesium.Viewer("view2D", options);

        // Add the same entity to both viewers. Each viewer will create separate WebGL resources to draw it.
        const greenCylinder = {
          name: "Green cylinder with black outline",
          position: Cesium.Cartesian3.fromDegrees(-100.0, 40.0, 200000.0),
          cylinder: {
            length: 400000.0,
            topRadius: 200000.0,
            bottomRadius: 200000.0,
            material: Cesium.Color.GREEN.withAlpha(0.5),
            outline: true,
            outlineColor: Cesium.Color.BLACK,
          },
        };

        view1.entities.add(greenCylinder);
        view2.entities.add(greenCylinder);

        // Add the same cylinder primitive to both viewers. Each will use the same WebGL resources to draw it.
        const cylinder = new Cesium.CylinderGeometry({
          length: 400000.0,
          topRadius: 200000.0,
          bottomRadius: 200000.0,
        });
        const geometry = Cesium.CylinderGeometry.createGeometry(cylinder);
        const primitive = new Cesium.Primitive({
          geometryInstances: new Cesium.GeometryInstance({
            geometry,
            modelMatrix: Cesium.Matrix4.multiplyByTranslation(
              Cesium.Transforms.eastNorthUpToFixedFrame(
                Cesium.Cartesian3.fromDegrees(-95.59777, 40.03883),
              ),
              new Cesium.Cartesian3(0.0, 0.0, 500000.0),
              new Cesium.Matrix4(),
            ),
            id: "red cylinder",
            attributes: {
              color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.RED),
            },
          }),
          appearance: new Cesium.PerInstanceColorAppearance(),
          asynchronous: false,
        });

        view1.scene.primitives.add(primitive);
        view2.scene.primitives.add(primitive);

        // Add the same tileset to both viewers. Each viewer will use the same WebGL resources to draw it.
        const tileset = await Cesium.Cesium3DTileset.fromIonAssetId(2464651);
        for (const view of [view1, view2]) {
          view.scene.primitives.add(tileset);
          view.zoomTo(
            tileset,
            new Cesium.HeadingPitchRange(0.5, -0.2, tileset.boundingSphere.radius * 4.0),
          );
        }
        //Sandcastle_End
        Sandcastle.finishedLoading();
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
      }
    </script>
  </body>
</html>
