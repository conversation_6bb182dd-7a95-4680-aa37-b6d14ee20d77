name: Report a bug
description: Let us know so we can fix it!
labels: ["needs triage", "type - bug"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for helping us improve CesiumJS! Please describe what the expected behavior is vs what actually happens.
  - type: textarea
    id: what-happened
    attributes:
      label: What happened?
      description: Also tell us, what did you expect to happen?
    validations:
      required: true
  - type: textarea
    id: repro
    attributes:
      label: Reproduction steps
      description: "How do you trigger this bug? Please walk us through it step by step."
      value: |
        1.
        2.
        3.
        ...
  - type: input
    id: sandcastle
    attributes:
      label: Sandcastle example
      description: Creating a Sandcastle example (https://sandcastle.cesium.com) that reproduces the issue helps us a lot in tracking down bugs. Paste the link you get from the "Share" button in Sandcastle below.
      placeholder: ex. https://sandcastle.cesium.com/...
    validations:
      required: false
  - type: textarea
    id: environment
    attributes:
      label: Environment
      description: What Browsers, OS, and CesiumJS version are you experiencing this issue on?
      value: |
        Browser:
        CesiumJS Version:
        Operating System:
  - type: markdown
    attributes:
      value: |
        If you can also contribute a fix, we'd absolutely appreciate it! Fixing a bug in CesiumJS often means fixing a bug for thousands of applications and millions of end users.

        Check out the contributor guide to get started: [CONTRIBUTING.md](https://github.com/CesiumGS/cesium/blob/main/CONTRIBUTING.md)

        Just let us know you're working on it and we'd be happy to provide advice and feedback.