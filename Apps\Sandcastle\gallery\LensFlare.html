﻿<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="Lens Flare." />
    <meta name="cesium-sandcastle-labels" content="Showcases, Post Processing" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar">
      <table>
        <tbody>
          <tr>
            <td>Lens Flare</td>
            <td><input type="checkbox" data-bind="checked: show" /></td>
          </tr>
          <tr>
            <td>Intensity</td>
            <td>
              <input
                type="range"
                min="0"
                max="10"
                step="0.01"
                data-bind="value: intensity, valueUpdate: 'input'"
              />
            </td>
          </tr>
          <tr>
            <td>Distortion</td>
            <td>
              <input
                type="range"
                min="1"
                max="20"
                step="0.01"
                data-bind="value: distortion, valueUpdate: 'input'"
              />
            </td>
          </tr>
          <tr>
            <td>Dispersion</td>
            <td>
              <input
                type="range"
                min="0"
                max="1"
                step="0.01"
                data-bind="value: dispersion, valueUpdate: 'input'"
              />
            </td>
          </tr>
          <tr>
            <td>Halo Width</td>
            <td>
              <input
                type="range"
                min="0"
                max="1"
                step="0.01"
                data-bind="value: haloWidth, valueUpdate: 'input'"
              />
            </td>
          </tr>
          <tr>
            <td>Dirt Amount</td>
            <td>
              <input
                type="range"
                min="0"
                max="1"
                step="0.01"
                data-bind="value: dirtAmount, valueUpdate: 'input'"
              />
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer");

        const viewModel = {
          show: true,
          intensity: 2.0,
          distortion: 10.0,
          dispersion: 0.4,
          haloWidth: 0.4,
          dirtAmount: 0.4,
        };

        Cesium.knockout.track(viewModel);
        const toolbar = document.getElementById("toolbar");
        Cesium.knockout.applyBindings(viewModel, toolbar);
        for (const name in viewModel) {
          if (viewModel.hasOwnProperty(name)) {
            Cesium.knockout.getObservable(viewModel, name).subscribe(updatePostProcess);
          }
        }

        const lensFlare = viewer.scene.postProcessStages.add(
          Cesium.PostProcessStageLibrary.createLensFlareStage(),
        );

        function updatePostProcess() {
          lensFlare.enabled = Boolean(viewModel.show);
          lensFlare.uniforms.intensity = Number(viewModel.intensity);
          lensFlare.uniforms.distortion = Number(viewModel.distortion);
          lensFlare.uniforms.ghostDispersal = Number(viewModel.dispersion);
          lensFlare.uniforms.haloWidth = Number(viewModel.haloWidth);
          lensFlare.uniforms.dirtAmount = Number(viewModel.dirtAmount);
          lensFlare.uniforms.earthRadius = Cesium.Ellipsoid.WGS84.maximumRadius;
        }
        updatePostProcess();

        const camera = viewer.scene.camera;
        camera.position = new Cesium.Cartesian3(
          40010447.97500168,
          56238683.46406788,
          20776576.752223067,
        );
        camera.direction = new Cesium.Cartesian3(
          -0.5549701431494752,
          -0.7801872010801355,
          -0.2886452346452218,
        );
        camera.up = new Cesium.Cartesian3(
          -0.3016252360948521,
          -0.13464820558887716,
          0.9438707950150912,
        );
        camera.right = Cesium.Cartesian3.cross(
          camera.direction,
          camera.up,
          new Cesium.Cartesian3(),
        );

        viewer.clock.currentTime = new Cesium.JulianDate(2458047, 27399.860215000022);
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
