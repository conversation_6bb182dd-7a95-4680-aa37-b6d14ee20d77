<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="View datasets underneath the surface with globe translucency."
    />
    <meta name="cesium-sandcastle-labels" content="Showcases" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
      #toolbar {
        background: rgba(42, 42, 42, 0.8);
        padding: 4px;
        border-radius: 4px;
      }
      #toolbar input {
        vertical-align: middle;
        padding-top: 2px;
        padding-bottom: 2px;
      }
      #toolbar .header {
        font-weight: bold;
      }
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar">
      <table>
        <tbody>
          <tr>
            <td>Translucency enabled</td>
            <td>
              <input type="checkbox" data-bind="checked: translucencyEnabled" />
            </td>
          </tr>
          <tr>
            <td>Fade by distance</td>
            <td>
              <input type="checkbox" data-bind="checked: fadeByDistance" />
            </td>
          </tr>
          <tr>
            <td>Show vector data</td>
            <td>
              <input type="checkbox" data-bind="checked: showVectorData" />
            </td>
          </tr>
          <tr>
            <td>Alpha</td>
            <td>
              <input
                type="range"
                min="0.0"
                max="1.0"
                step="0.1"
                data-bind="value: alpha, valueUpdate: 'input'"
              />
              <input type="text" size="5" data-bind="value: alpha" />
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer", {
          terrain: Cesium.Terrain.fromWorldTerrain(),
        });

        const scene = viewer.scene;
        const globe = scene.globe;

        scene.screenSpaceCameraController.enableCollisionDetection = false;
        globe.translucency.frontFaceAlphaByDistance = new Cesium.NearFarScalar(
          400.0,
          0.0,
          800.0,
          1.0,
        );

        const longitude = -3.82518;
        const latitude = 53.11728;
        const height = 72.8;
        const position = Cesium.Cartesian3.fromDegrees(longitude, latitude, height);
        const url = "../../SampleData/models/ParcLeadMine/ParcLeadMine.glb";

        const entity = viewer.entities.add({
          name: url,
          position: position,
          model: {
            uri: url,
          },
        });

        const polygon = viewer.entities.add({
          polygon: {
            hierarchy: new Cesium.PolygonHierarchy(
              Cesium.Cartesian3.fromDegreesArrayHeights([
                -3.8152789692233817, 53.124521420389996, 200.20779492422255,
                -3.8165955002619016, 53.12555934545405, 205.85834336951655,
                -3.8201599842222054, 53.12388420656903, 230.82362697069453,
                -3.8198667503545027, 53.123748567587455, 225.53297006293968,
                -3.8190548496317476, 53.1240486000822, 221.82677773619432,
                -3.817536387097508, 53.122763476393764, 209.94136782255705,
                -3.8169125359199336, 53.12285547981627, 210.96626238861327,
                -3.8166873871853073, 53.12299403424474, 211.02223937734595,
                -3.8163695374580873, 53.12300505277307, 211.25942926271824,
                -3.8162743040622313, 53.12281471203994, 212.35109129094147,
                -3.8159746138174193, 53.12280996651767, 214.87977416348798,
                -3.815429896849304, 53.1236135347983, 209.72496223706005,
              ]),
            ),
            material: Cesium.Color.LIME.withAlpha(0.5),
            classificationType: Cesium.ClassificationType.TERRAIN,
          },
        });

        const polyline = viewer.entities.add({
          polyline: {
            positions: Cesium.Cartesian3.fromDegreesArrayHeights([
              -3.8098444201746373, 53.1190304262546, 286.1875170545701,
              -3.8099801237370663, 53.119539531697576, 288.7733884242394,
              -3.810165716635671, 53.11979180761567, 290.9294630315179,
              -3.8104840812145357, 53.12007534956926, 292.6392327626228,
              -3.8105689502073554, 53.120259094792196, 292.222036965774,
              -3.811027311824268, 53.120409248874196, 289.61356291617307,
              -3.811530473295422, 53.12063281057782, 284.01098712543586,
              -3.8120545342562693, 53.120742539082435, 280.118191867836,
              -3.812444493044727, 53.120813289759326, 276.0400221387852,
              -3.812779626711285, 53.12094275348024, 271.1187399484896,
              -3.8133560322579494, 53.12104757866638, 263.3495497598578,
              -3.8137266493960085, 53.12120789867194, 257.73878624321316,
              -3.8142552291751133, 53.121321248522904, 251.87265828778177,
              -3.814322603988525, 53.12174170121103, 238.7082749547689,
              -3.8143764268391314, 53.1219492923309, 235.0371831845662,
              -3.8148156514145786, 53.12210819668669, 230.2458816627467,
              -3.8155394721966163, 53.1222990144029, 221.33319292262706,
              -3.8159828072920927, 53.12203093429715, 223.66664756982703,
              -3.816678108944717, 53.12183939425214, 223.8787312412801,
              -3.817466081093726, 53.121751900508535, 224.52293229989735,
              -3.8183082996527955, 53.12173266141031, 223.3672181535749,
            ]),
            width: 8,
            material: new Cesium.PolylineOutlineMaterialProperty({
              color: Cesium.Color.YELLOW,
              outlineWidth: 2,
              outlineColor: Cesium.Color.BLACK,
            }),
            clampToGround: true,
          },
        });

        const viewModel = {
          translucencyEnabled: true,
          fadeByDistance: true,
          showVectorData: false,
          alpha: 0.5,
        };

        Cesium.knockout.track(viewModel);
        const toolbar = document.getElementById("toolbar");
        Cesium.knockout.applyBindings(viewModel, toolbar);
        for (const name in viewModel) {
          if (viewModel.hasOwnProperty(name)) {
            Cesium.knockout.getObservable(viewModel, name).subscribe(update);
          }
        }

        function update() {
          globe.translucency.enabled = viewModel.translucencyEnabled;

          let alpha = Number(viewModel.alpha);
          alpha = !isNaN(alpha) ? alpha : 1.0;
          alpha = Cesium.Math.clamp(alpha, 0.0, 1.0);

          globe.translucency.frontFaceAlphaByDistance.nearValue = alpha;
          globe.translucency.frontFaceAlphaByDistance.farValue = viewModel.fadeByDistance
            ? 1.0
            : alpha;

          polygon.show = viewModel.showVectorData;
          polyline.show = viewModel.showVectorData;
        }
        update();

        viewer.scene.camera.setView({
          destination: new Cesium.Cartesian3(
            3826465.9884728403,
            -254831.02751468265,
            5081387.671561018,
          ),
          orientation: new Cesium.HeadingPitchRoll(
            3.3889450556243754,
            -0.5276382514771969,
            6.282272566663295,
          ),
          endTransform: Cesium.Matrix4.IDENTITY,
        });

        viewer.scene.camera.flyTo({
          destination: new Cesium.Cartesian3(
            3827270.552916987,
            -255123.18143177085,
            5079147.091351856,
          ),
          orientation: new Cesium.HeadingPitchRoll(
            3.2624281242239963,
            -0.22213535190506972,
            6.282786783842843,
          ),
          duration: 0.0,
        }); //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
