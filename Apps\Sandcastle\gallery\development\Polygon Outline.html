<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="Draw the outline of a polygon." />
    <meta name="cesium-sandcastle-labels" content="Development" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        // Create the viewer.
        const viewer = new Cesium.Viewer("cesiumContainer");
        const scene = viewer.scene;

        // Example 1: Draw the outline of a polygon on the globe surface.
        let positions = Cesium.Cartesian3.fromDegreesArray([
          -95, 37.0, -95, 32.0, -90, 33.0, -87, 31.0, -87, 35.0,
        ]);
        const polygonOutlineInstance = new Cesium.GeometryInstance({
          geometry: Cesium.PolygonOutlineGeometry.fromPositions({
            positions: positions,
          }),
          attributes: {
            color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.WHITE),
          },
        });

        // Example 2: Draw a polygon outline with holes.

        // To draw a polygon with holes, create a nested position
        // hierarchy defining the positions of the polygon
        // edges and the positions of the holes.
        const polygonHierarchy = {
          positions: Cesium.Cartesian3.fromDegreesArray([
            -108.0, 30.0, -98.0, 30.0, -98.0, 40.0, -108.0, 40.0,
          ]),
          holes: [
            {
              positions: Cesium.Cartesian3.fromDegreesArray([
                -106.0, 31.0, -106.0, 39.0, -100.0, 39.0, -100.0, 31.0,
              ]),
            },
          ],
        };
        // To extrude, use the extrudedHeight option to specify the
        // height of the polygon.
        const extrudedPolygonOutlineInstance = new Cesium.GeometryInstance({
          geometry: new Cesium.PolygonOutlineGeometry({
            polygonHierarchy: polygonHierarchy,
            extrudedHeight: 500000.0,
          }),
          attributes: {
            color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.WHITE),
          },
        });

        // Example 3: Draw a polygon outline with per position heights.
        positions = Cesium.Cartesian3.fromDegreesArrayHeights([
          -95, 44.0, 400000, -95, 39.0, 100000, -87, 42.0, 100000,
        ]);
        // Set the perPositionHeight option to true for the polygon
        // to use the heights each position.
        const perPositionPolygonOutlineInstance = new Cesium.GeometryInstance({
          geometry: Cesium.PolygonOutlineGeometry.fromPositions({
            positions: positions,
            perPositionHeight: true,
          }),
          attributes: {
            color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.WHITE),
          },
        });

        // Add all polyline outlines instances to primitives.
        scene.primitives.add(
          new Cesium.Primitive({
            geometryInstances: [
              polygonOutlineInstance,
              extrudedPolygonOutlineInstance,
              perPositionPolygonOutlineInstance,
            ],
            appearance: new Cesium.PerInstanceColorAppearance({
              flat: true,
              renderState: {
                lineWidth: Math.min(2.0, scene.maximumAliasedLineWidth),
              },
            }),
          }),
        );
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
