<?xml version="1.0" ?>
<!--
	Source file for tabBottomUnselected.png, which is used by IE7-9 for the selected tabs.
	Compile to png with batik, gimp, or online tool ex: http://www.fileformat.info/convert/image/svg2raster.htm

	Output should match CSS gradient from TabContainer.less.
-->
<svg xmlns="http://www.w3.org/2000/svg" width="1px" height="16px" viewBox="0 0 1 16" preserveAspectRatio="none">
	<defs>
		<linearGradient id="gradient" gradientUnits="objectBoundingBox" x1="0%" y1="100%" x2="0%" y2="0%">
			<stop offset="0%" stop-color="#ffffff" stop-opacity="1"/>
			<stop offset="6%" stop-color="#ffffff" stop-opacity="1"/>
			<stop offset="13%" stop-color="#ffffff" stop-opacity="0.2"/>
			<stop offset="43%" stop-color="#ffffff" stop-opacity="0.6"/>
			<stop offset="100%" stop-color="#ffffff" stop-opacity="0"/>
		</linearGradient>
	</defs>
	<rect x="0" y="0" width="1" height="16" fill="url(#gradient)"/>
</svg>