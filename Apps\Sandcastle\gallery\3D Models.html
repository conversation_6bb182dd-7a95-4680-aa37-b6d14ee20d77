<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="Create 3D models using glTF." />
    <meta name="cesium-sandcastle-labels" content="Tutorials,Showcases" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer", {
          infoBox: false,
          selectionIndicator: false,
          shadows: true,
          shouldAnimate: true,
        });

        function createModel(url, height) {
          viewer.entities.removeAll();

          const position = Cesium.Cartesian3.fromDegrees(
            -123.0744619,
            44.0503706,
            height,
          );
          const heading = Cesium.Math.toRadians(135);
          const pitch = 0;
          const roll = 0;
          const hpr = new Cesium.HeadingPitchRoll(heading, pitch, roll);
          const orientation = Cesium.Transforms.headingPitchRollQuaternion(position, hpr);

          const entity = viewer.entities.add({
            name: url,
            position: position,
            orientation: orientation,
            model: {
              uri: url,
              minimumPixelSize: 128,
              maximumScale: 20000,
            },
          });
          viewer.trackedEntity = entity;
        }

        const options = [
          {
            text: "Aircraft",
            onselect: function () {
              createModel("../../SampleData/models/CesiumAir/Cesium_Air.glb", 5000.0);
            },
          },
          {
            text: "Drone",
            onselect: function () {
              createModel("../../SampleData/models/CesiumDrone/CesiumDrone.glb", 150.0);
            },
          },
          {
            text: "Ground Vehicle",
            onselect: function () {
              createModel("../../SampleData/models/GroundVehicle/GroundVehicle.glb", 0);
            },
          },
          {
            text: "Hot Air Balloon",
            onselect: function () {
              createModel(
                "../../SampleData/models/CesiumBalloon/CesiumBalloon.glb",
                1000.0,
              );
            },
          },
          {
            text: "Milk Truck",
            onselect: function () {
              createModel(
                "../../SampleData/models/CesiumMilkTruck/CesiumMilkTruck.glb",
                0,
              );
            },
          },
          {
            text: "Skinned Character",
            onselect: function () {
              createModel("../../SampleData/models/CesiumMan/Cesium_Man.glb", 0);
            },
          },
          {
            text: "Unlit Box",
            onselect: function () {
              createModel("../../SampleData/models/BoxUnlit/BoxUnlit.gltf", 10.0);
            },
          },
          {
            text: "Draco Compressed Model",
            onselect: function () {
              createModel(
                "../../SampleData/models/DracoCompressed/CesiumMilkTruck.gltf",
                0,
              );
            },
          },
          {
            text: "KTX2 Compressed Balloon",
            onselect: function () {
              if (!Cesium.FeatureDetection.supportsBasis(viewer.scene)) {
                window.alert(
                  "This browser does not support Basis Universal compressed textures",
                );
              }
              createModel(
                "../../SampleData/models/CesiumBalloonKTX2/CesiumBalloonKTX2.glb",
                1000.0,
              );
            },
          },
          {
            text: "Instanced Box",
            onselect: function () {
              createModel("../../SampleData/models/BoxInstanced/BoxInstanced.gltf", 15);
            },
          },
        ];

        Sandcastle.addToolbarMenu(options);
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
