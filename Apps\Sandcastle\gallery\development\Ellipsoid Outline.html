<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="Draw the outline of an ellipsoid." />
    <meta name="cesium-sandcastle-labels" content="Development" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        // Create the viewer.
        const viewer = new Cesium.Viewer("cesiumContainer");
        const scene = viewer.scene;

        // Draw a the outline of an ellipsoid and position
        // it on the globe surface.

        const radii = new Cesium.Cartesian3(200000.0, 200000.0, 300000.0);
        const positionOnEllipsoid = Cesium.Cartesian3.fromDegrees(-102.0, 45.0);
        const modelMatrix = Cesium.Matrix4.multiplyByTranslation(
          Cesium.Transforms.eastNorthUpToFixedFrame(positionOnEllipsoid),
          new Cesium.Cartesian3(0.0, 0.0, radii.z),
          new Cesium.Matrix4(),
        );
        // Create a ellipsoid geometry.
        const ellipsoidOutlineGeometry = new Cesium.EllipsoidOutlineGeometry({
          radii: radii,
          stackPartitions: 16,
          slicePartitions: 8,
        });
        // Create a geometry instance using the geometry
        // and model matrix created above.
        const ellipseOutlineInstance = new Cesium.GeometryInstance({
          geometry: ellipsoidOutlineGeometry,
          modelMatrix: modelMatrix,
          attributes: {
            color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.WHITE),
          },
        });
        // Add the geometry instance to primitives.
        scene.primitives.add(
          new Cesium.Primitive({
            geometryInstances: ellipseOutlineInstance,
            appearance: new Cesium.PerInstanceColorAppearance({
              flat: true,
              renderState: {
                lineWidth: Math.min(2.0, scene.maximumAliasedLineWidth),
              },
            }),
          }),
        );
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
