<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="Draw the outline of a cylinder." />
    <meta name="cesium-sandcastle-labels" content="Development" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        // Create the viewer.
        const viewer = new Cesium.Viewer("cesiumContainer");
        const scene = viewer.scene;

        // Create a cyliner outline and position on the globe
        // surface using a model matrix.

        // Create the model matrix.
        const length = 400000.0;
        const positionOnEllipsoid = Cesium.Cartesian3.fromDegrees(-100.0, 40.0);
        const modelMatrix = Cesium.Matrix4.multiplyByTranslation(
          Cesium.Transforms.eastNorthUpToFixedFrame(positionOnEllipsoid),
          new Cesium.Cartesian3(0.0, 0.0, length * 0.5),
          new Cesium.Matrix4(),
        );
        // Create the cylinder outline geometry.  The numberOfVerticalLines
        // option can be used to specify the number of lines connecting
        // the top and bottom of the cylinder.
        const cylinderOutlineGeometry = new Cesium.CylinderOutlineGeometry({
          length: length,
          topRadius: 150000.0,
          bottomRadius: 150000.0,
          numberOfVerticalLines: 16,
        });
        // Create a geometry instance using the geometry.
        const cylinderOutline = new Cesium.GeometryInstance({
          geometry: cylinderOutlineGeometry,
          modelMatrix: modelMatrix,
          attributes: {
            color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.WHITE),
          },
        });
        // Add the instance to primitives.
        scene.primitives.add(
          new Cesium.Primitive({
            geometryInstances: cylinderOutline,
            appearance: new Cesium.PerInstanceColorAppearance({
              flat: true,
              renderState: {
                lineWidth: Math.min(2.0, scene.maximumAliasedLineWidth),
              },
            }),
          }),
        );
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
