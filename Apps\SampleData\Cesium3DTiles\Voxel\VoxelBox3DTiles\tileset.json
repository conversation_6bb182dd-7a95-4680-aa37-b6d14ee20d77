{"asset": {"version": "1.1"}, "schema": {"id": "voxel", "classes": {"voxel": {"properties": {"a": {"type": "VEC4", "componentType": "FLOAT32"}}}}}, "statistics": {"classes": {"voxel": {"count": 8, "properties": {"a": {"min": 0.0, "max": 1.0}}}}}, "geometricError": 0.0, "root": {"boundingVolume": {"box": [0.0, 0.0, 0.0, 6378137.0, 0.0, 0.0, 0.0, 6378137.0, 0.0, 0.0, 0.0, 6378137.0]}, "geometricError": 16.0, "refine": "REPLACE", "content": {"uri": "tiles/{level}/{x}/{y}/{z}.gltf", "extensions": {"3DTILES_content_voxels": {"dimensions": [2, 2, 2], "class": "voxel"}}}, "implicitTiling": {"subdivisionScheme": "OCTREE", "subtreeLevels": 1, "availableLevels": 1, "subtrees": {"uri": "subtrees/{level}/{x}/{y}/{z}.json"}}}, "extensionsUsed": ["3DTILES_content_voxels"], "extensionsRequired": ["3DTILES_content_voxels"]}