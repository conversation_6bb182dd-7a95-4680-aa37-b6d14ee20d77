# Presenter's Guide

The Cesium team gives a lot of [talks](https://cesium.com/learn/presentations/). Here's some tips, based on our experience, for delivering a great talk.

- [Invest Time Preparing](#invest-time-preparing)
- [Know the Audience](#know-the-audience)
- [Provide Context](#provide-context)
- [Tell a Story](#tell-a-story)
- [You're Not Too Short on Time](#youre-not-too-short-on-time)
- [Use "Short" Slides](#use-short-slides)
- [Inspire. Don't Teach](#inspire-dont-teach)
- [Finish with a Call to Action](#finish-with-a-call-to-action)
- [Practice](#practice)
- [Test the Projector](#test-the-projector)
- [Engage the Audience](#engage-the-audience)
- [Be Passionate](#be-passionate)
- [You Are the Expert](#you-are-the-expert)
- [Present with a Partner](#present-with-a-partner)
- [Resources](#resources)

## Invest Time Preparing

Given how much value a talk can bring, it is worth investing time to prepare. If you give a 30-minute talk to 100 people, **they cumulatively spend 50 hours watching it**. If you post the video and slides online, many others will also spend their time.

People may blog about your talk. They might cite it in their own talks.
And you may use it again, too. Your future talks may be based on your previous talks. You may also create website content from it.

Put in the time. A one-hour talk from scratch on a deep technical topic can take 15 hours to prepare. A 30-minute Cesium overview based on a previous talk may take only two hours to update.

## Know the Audience

You'll give a different talk to a group of researchers than to a group of developers than to a group of users. Find out as much as you can about the audience. How much do they already know about the topic? What are their goals in attending your talk? What do you want them to do as a result of listening to your talk?

If you are presenting at a conference, ask the organizers and people who have presented there. If you are presenting at an organization, ask your point of contact. The mistake often made isn't forgetting to tailor the talk to the audience; **the mistake is tailoring it to the wrong audience**.

## Provide Context

Your topic is probably second nature to you; you work on it all day. Your audience does not.

Things that are obvious to you are often not obvious to them.

Focus less on details, and instead **focus on providing context**, especially at the start of your talk. Otherwise, you will lose the audience the moment they [can't see the forest for the trees](http://www.urbandictionary.com/define.php?term=can%27t+see+the+forest+for+the+trees).

## Tell a Story

Your audience loves stories. Tell one.

Early in a presentation, **a story will help build context**. For example, see Slides 2-6 of [3D Tiles](https://cesium.com/presentations/files/SIGGRAPH2015/Cesium3DTiles.pdf).

Stories will help you connect with your audience. It will make them more likely to [engage you](#engage-the-audience). And it will make them more likely to remember your talk later.

## You're Not Too Short on Time

Novice presenters, in particular, often think they need a lot of material. They worry about running short.

In the vast majority of cases, the exact opposite happens; the talk includes too much material for the allowed time, and the presenter is forced go too fast, minimize questions, and cut material.

**Keep it short**.

Slow down.

If it breaks your heart to cut slides, move them to the end as _Bonus Slides_. It is often a good idea to store the extra details, including links and references, in your bonus slides so you feel prepared to answer any tough questions that may come up.

## Use "Short" Slides

Good writing uses short paragraphs so readers feels like they are constantly making progress.

Good presenters do the same. **Don't spend too much time on any one slide**; the audience will tune you out. Break uber slides into multiple slides.

Think of each slide and the entire presentation as a one-page resume; a resume is only one-page so it highlights your best work. A longer resume leaves your best work to get lost in the noise of all your work.

**A slide with too many words overwhelms the audience**. The audience will likely try to read it and tune you out in the process - or they may just feel that this part of the talk is too much work to take in, and tune everything out. Replace phrases with keywords: as a rule of thumb, if your font is smaller than 18 pt., you have too many words.

Whenever possible, **replace words with images**. Put the words in the notes section for posting online and for your review next time you give a similar talk.

When reasonable, replace images with videos, and replace videos with demos. Demos need to be carefully practiced so they can be delivered smoothly. Long pauses as you navigate the UI will tune out the audience.

In general, avoid fancy animation on slides.

## Inspire. Don't Teach

Novice presenters often think they need to go really deep into the material. This is wrong for nearly all talks.

Even a presentation for a research paper includes far less detail than the paper; _it is a pitch to read the paper_. Think of your talk as a teaser that tells the audience where to go for more information.

**Too much detail will overwhelm your audience** and likely put you in a time crunch. Instead, provide enough [context and detail](#provide-context) to inspire the audience to take your [call to action](#finish-with-a-call-to-action).

## Finish with a Call to Action

Use the last slide to pitch a call to action. Do you want the audience to check out Cesium? Provide feedback on a new format? Apply for a job?

Ask them. But do not ask them to do too many things. **What is the one most important action? Ask for it.**

## Practice

A novice speaker will benefit significantly from multiple dry runs.

First, ask your colleagues for feedback on your slides.

Then, organize a _timed_ dry run. Invite your colleagues and ask them to take notes and provide feedback on both your content and delivery.

Then iterate with multiple dry runs until you are satisfied or run out of time.

A dry run with colleagues is harder than the real presentation. **Expect your presentation to go better than your best dry run.**

## Test the Projector

Present on your own laptop whenever possible.

Ask in advance about the projector connections and Internet access. Bring adapters: not everyone has HDMI.

Ask for a time when you can test the projector. For a conference, perhaps this is before the morning session or during a break.

## Engage the Audience

For an audience of 20 or fewer, a great talk will turn into a group discussion with you as the facilitator.

Engage the audience.

Ask them questions.

**[Tell stories](#tell-a-story) to create an inclusive, inviting environment**.

Ask, "Who has the first question?" not, "Are there any questions?"

## Be Passionate

Give talks on topics you are passionate about. **Be excited.** Your passion will naturally show through and will inspire your audience.

As you speak, project your voice and speak to the people, not to the screen. Don't feel tied to the podium or to your laptop. Feel free to step out and talk to the audience.

The first time you give a talk is often the best because the material is fresh to you.

To deliver the same talk well a second time, it is important to spend the time to review and update the material so you are not presenting cold and recalling what to say on the fly.

Once you've spoken about a topic many times, it may be time to encourage someone else to take the lead.

## You Are the Expert

In almost all talks you give, you will be the expert on the topic in the room, even if you don't think you are. You prepared the talk. You have some experience with the topic even if you are not the originator of the work (this is common in grad school where students present recently published research papers).

**Be confident**. But don't be arrogant.

The more confident you are, the more your personality will come out. Your passion, humor, or energy will impress the audience as much as anything you may say.

Don't worry about failing. You won't.

## Present with a Partner

When presenting with a partner, strive to make it like a conversation. For a great example, see how [Brian Fitzpatrick and Ben Collins-Sussman](https://www.youtube.com/watch?v=OTCuYzAw31Y) do it.

## Resources

- [10 tips for academic talks](http://matt.might.net/articles/academic-presentation-tips/) by Matt Might applies equally well to Cesium talks.
- [Presentation Advice](http://cs.williams.edu/~morgan/presentation-advice.pdf) by Morgan McGuire
- [Tips for Giving Clear Talks](https://graphics.stanford.edu/~kayvonf/misc/cleartalktips.pdf) by Kayvon Fatahalian
