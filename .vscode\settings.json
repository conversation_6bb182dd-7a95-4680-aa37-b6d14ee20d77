// Cesium project settings for VSCode.
{
    "files.exclude": {
        "**/.git": true,
        "**/.DS_Store": true,
        "**/Thumbs.db": true,
        ".idea": true,
        ".metadata": true,
        ".settings": true,
        ".externalToolBuilders": true,
        ".project": true,
        "launches": true
    },
    "search.exclude": {
        "Build": true,
        "node_modules": true,
        "Source/Shaders/**/*.js": true
    },
    "files.associations": {
        "*.czml": "json"
    },
    "eslint.enable": true,
    "javascript.format.insertSpaceAfterCommaDelimiter": true,
    "javascript.format.insertSpaceAfterSemicolonInForStatements": true,
    "javascript.format.insertSpaceBeforeAndAfterBinaryOperators": true,
    "javascript.format.insertSpaceAfterKeywordsInControlFlowStatements": true,
    "javascript.format.insertSpaceAfterFunctionKeywordForAnonymousFunctions": false,
    "javascript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyParenthesis": false,
    "javascript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyBrackets": false,
    "javascript.format.placeOpenBraceOnNewLineForFunctions": false,
    "javascript.format.placeOpenBraceOnNewLineForControlBlocks": false,
    "glTF.defaultV2Engine": "Cesium"
}
