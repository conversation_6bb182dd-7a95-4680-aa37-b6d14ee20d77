{"version": "0.2", "language": "en", "allowCompoundWords": true, "caseSensitive": false, "files": ["**/*.js", "**/*.html", "**/*.css", "**/*.md"], "ignorePaths": ["node_modules/**", "**/ThirdParty/**", "**/Build/**", "Source/**", "CONTRIBUTORS.md", "**/LICENSE.md"], "useGitignore": true, "dictionaries": ["typescript", "node", "html", "css", "packages"], "dictionaryDefinitions": [{"name": "packages", "path": "./.cspell/cspell-packages.txt", "addWords": false}], "words": ["3DTILES", "aabb", "Amato", "basisu", "bathymetric", "bitangent", "bitangents", "bivariate", "<PERSON><PERSON><PERSON>", "brdf", "cartesians", "carto", "cartographics", "cesium<PERSON>s", "comms", "cyclomatic", "czml", "dequantize", "dequantized", "dequantizes", "dijit", "DONT", "ecef", "Eigen", "emscripten", "EPSG", "fxaa", "gdal", "glsl", "gltf", "iframes", "iife", "<PERSON><PERSON><PERSON><PERSON>", "lerp", "<PERSON><PERSON>", "maki", "MAXAR", "minifiers", "mipmapped", "mipmaps", "msaa", "noaa", "Occludee", "Occluder", "occluders", "octree", "octrees", "OITFS", "pako", "phong", "<PERSON><PERSON><PERSON><PERSON>", "pnts", "quantizations", "reproject", "slerp", "tada", "teme", "tipsify", "topo", "<PERSON><PERSON><PERSON><PERSON>", "Transitioner", "tridiagonal", "tweens", "uncentered", "uncompress", "unminified", "unproject", "unregisters", "unrenderable", "voxel", "VVLH", "WEBG", "WMTS", "xdescribe"]}