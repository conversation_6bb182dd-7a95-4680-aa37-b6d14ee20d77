{"asset": {"version": "1.0"}, "geometricError": 240, "root": {"boundingVolume": {"region": [-1.3197209591796106, 0.6988424218, -1.3196390408203893, 0.6989055782, 0, 20]}, "geometricError": 70, "refine": "ADD", "children": [{"boundingVolume": {"region": [-1.3197209591796106, 0.6988424218, -1.31968, 0.698874, 0, 20]}, "geometricError": 0, "content": {"uri": "ll.b3dm"}}, {"boundingVolume": {"region": [-1.31968, 0.6988424218, -1.3196390408203893, 0.698874, 0, 20]}, "geometricError": 0, "content": {"uri": "lr.b3dm"}}, {"boundingVolume": {"region": [-1.31968, 0.698874, -1.3196390408203893, 0.6989055782, 0, 20]}, "geometricError": 0, "content": {"uri": "ur.b3dm"}}, {"boundingVolume": {"region": [-1.3197209591796106, 0.698874, -1.31968, 0.6989055782, 0, 20]}, "geometricError": 0, "content": {"uri": "ul.b3dm"}}, {"transform": [0.9686356343768793, 0.24848542777253738, 0, 0, -0.1598646089326599, 0.6231776176011753, 0.7655670863691378, 0, 0.19023226494501025, -0.7415555603632288, 0.643356072690908, 0, 1215014.7852103356, -4736320.466755246, 4081611.654821087, 1], "viewerRequestVolume": {"sphere": [0, 0, 0, 1000]}, "boundingVolume": {"sphere": [0, 0, 0, 10]}, "geometricError": 0, "content": {"uri": "points.pnts"}}]}}