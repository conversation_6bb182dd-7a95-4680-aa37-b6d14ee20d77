<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="description" content="Draw a frustum." />
    <meta name="cesium-sandcastle-labels" content="Development" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        // Create the viewer.
        const viewer = new Cesium.Viewer("cesiumContainer");
        const scene = viewer.scene;

        const positionOnEllipsoid = Cesium.Cartesian3.fromDegrees(-105.0, 45.0, 20.0);

        const enu = Cesium.Transforms.eastNorthUpToFixedFrame(positionOnEllipsoid);
        const rotation = Cesium.Matrix4.getMatrix3(enu, new Cesium.Matrix3());
        Cesium.Matrix3.multiply(
          rotation,
          Cesium.Matrix3.fromRotationX(-Cesium.Math.PI_OVER_TWO),
          rotation,
        );
        const orientation = Cesium.Quaternion.fromRotationMatrix(rotation);

        const frustum = new Cesium.PerspectiveFrustum({
          fov: Cesium.Math.toRadians(60.0),
          aspectRatio: scene.canvas.clientWidth / scene.canvas.clientHeight,
          near: 10.0,
          far: 50.0,
        });

        const frustumGeometry = new Cesium.FrustumGeometry({
          frustum: frustum,
          origin: positionOnEllipsoid,
          orientation: orientation,
          vertexFormat: Cesium.VertexFormat.POSITION_ONLY,
        });

        const frustumGeometryInstance = new Cesium.GeometryInstance({
          geometry: frustumGeometry,
          attributes: {
            color: Cesium.ColorGeometryInstanceAttribute.fromColor(
              new Cesium.Color(1.0, 0.0, 0.0, 0.5),
            ),
          },
          id: "frustum",
        });

        const frustumPrimitive = scene.primitives.add(
          new Cesium.Primitive({
            geometryInstances: frustumGeometryInstance,
            appearance: new Cesium.PerInstanceColorAppearance({
              closed: true,
              flat: true,
            }),
          }),
        );
        scene.primitives.add(
          new Cesium.Primitive({
            geometryInstances: new Cesium.GeometryInstance({
              geometry: new Cesium.FrustumOutlineGeometry({
                frustum: frustum,
                origin: positionOnEllipsoid,
                orientation: orientation,
              }),
              attributes: {
                color: Cesium.ColorGeometryInstanceAttribute.fromColor(
                  new Cesium.Color(0.0, 0.0, 0.0, 1.0),
                ),
              },
            }),
            appearance: new Cesium.PerInstanceColorAppearance({
              flat: true,
            }),
          }),
        );

        const removeListener = scene.postRender.addEventListener(() => {
          if (!frustumPrimitive.ready) {
            return;
          }

          const bs =
            frustumPrimitive.getGeometryInstanceAttributes("frustum").boundingSphere;
          scene.camera.viewBoundingSphere(bs);
          scene.camera.lookAtTransform(Cesium.Matrix4.IDENTITY);

          removeListener();
        });
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
