<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="Draw a polyline volume- a shape extruded along a polyline."
    />
    <meta name="cesium-sandcastle-labels" content="Development" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar"></div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        // Create the viewer.
        const viewer = new Cesium.Viewer("cesiumContainer");
        const scene = viewer.scene;

        // Example 1: Draw a red tube on the globe surface.

        // Create positions for a 2D circle.
        function computeCircle(radius) {
          const positions = [];
          for (let i = 0; i < 360; i++) {
            const radians = Cesium.Math.toRadians(i);
            positions.push(
              new Cesium.Cartesian2(
                radius * Math.cos(radians),
                radius * Math.sin(radians),
              ),
            );
          }
          return positions;
        }
        // Create the polyline volume geometry instance.  The shape defined by the
        // shapePositions option will be extruded along the polylinePositions.
        const redTube = new Cesium.GeometryInstance({
          geometry: new Cesium.PolylineVolumeGeometry({
            polylinePositions: Cesium.Cartesian3.fromDegreesArray([
              -85.0, 32.0, -85.0, 36.0, -89.0, 36.0,
            ]),
            vertexFormat: Cesium.PerInstanceColorAppearance.VERTEX_FORMAT,
            shapePositions: computeCircle(60000.0),
          }),
          attributes: {
            color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.RED),
          },
        });

        // Example 2: Draw a green extruded square.

        // Create positions for a 2D square
        function boxPositions() {
          return [
            new Cesium.Cartesian2(-50000, -50000),
            new Cesium.Cartesian2(50000, -50000),
            new Cesium.Cartesian2(50000, 50000),
            new Cesium.Cartesian2(-50000, 50000),
          ];
        }
        // Create the polyline volume geometry instance.  The corderType option
        // can be set to draw rounded, beveled or mitered corners.
        const greenBox = new Cesium.GeometryInstance({
          geometry: new Cesium.PolylineVolumeGeometry({
            polylinePositions: Cesium.Cartesian3.fromDegreesArrayHeights([
              -90.0, 32.0, 0.0, -90.0, 36.0, 100000.0, -94.0, 36.0, 0.0,
            ]),
            vertexFormat: Cesium.PerInstanceColorAppearance.VERTEX_FORMAT,
            shapePositions: boxPositions(),
            cornerType: Cesium.CornerType.BEVELED,
          }),
          attributes: {
            color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.GREEN),
          },
        });

        // Example 3: Draw a blue extruded star.

        // Create positions for a 2D star.
        function starPositions(arms, rOuter, rInner) {
          const angle = Math.PI / arms;
          const pos = [];
          for (let i = 0; i < 2 * arms; i++) {
            const r = i % 2 === 0 ? rOuter : rInner;
            const p = new Cesium.Cartesian2(
              Math.cos(i * angle) * r,
              Math.sin(i * angle) * r,
            );
            pos.push(p);
          }
          return pos;
        }
        // Create the polyline volume geometry instance.
        const blueStar = new Cesium.GeometryInstance({
          geometry: new Cesium.PolylineVolumeGeometry({
            polylinePositions: Cesium.Cartesian3.fromDegreesArrayHeights([
              -95.0, 32.0, 0.0, -95.0, 36.0, 100000.0, -99.0, 36.0, 200000.0,
            ]),
            vertexFormat: Cesium.PerInstanceColorAppearance.VERTEX_FORMAT,
            shapePositions: starPositions(7, 70000, 50000),
            cornerType: Cesium.CornerType.ROUNDED,
          }),
          attributes: {
            color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.BLUE),
          },
        });

        // Add all instances to primitives.
        scene.primitives.add(
          new Cesium.Primitive({
            geometryInstances: [redTube, greenBox, blueStar],
            appearance: new Cesium.PerInstanceColorAppearance({
              translucent: false,
              closed: true,
            }),
          }),
        );
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
