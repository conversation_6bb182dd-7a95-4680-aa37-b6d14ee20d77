<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="Adjust brightness, contrast, hue, saturation, and gamma of an imagery layer."
    />
    <meta name="cesium-sandcastle-labels" content="Showcases" />
    <title>Cesium Demo</title>
    <script type="text/javascript" src="../Sandcastle-header.js"></script>
    <script
      type="text/javascript"
      src="../../../Build/CesiumUnminified/Cesium.js"
      nomodule
    ></script>
    <script type="module" src="../load-cesium-es6.js"></script>
  </head>
  <body class="sandcastle-loading" data-sandcastle-bucket="bucket-requirejs.html">
    <style>
      @import url(../templates/bucket.css);
      #toolbar {
        background: rgba(42, 42, 42, 0.8);
        padding: 4px;
        border-radius: 4px;
      }
      #toolbar input {
        vertical-align: middle;
        padding-top: 2px;
        padding-bottom: 2px;
      }
    </style>
    <div id="cesiumContainer" class="fullSize"></div>
    <div id="loadingOverlay"><h1>Loading...</h1></div>
    <div id="toolbar">
      <table>
        <tbody>
          <tr>
            <td>Brightness</td>
            <td>
              <input
                type="range"
                min="0"
                max="3"
                step="0.02"
                data-bind="value: brightness, valueUpdate: 'input'"
              />
              <input type="text" size="5" data-bind="value: brightness" />
            </td>
          </tr>
          <tr>
            <td>Contrast</td>
            <td>
              <input
                type="range"
                min="0"
                max="3"
                step="0.02"
                data-bind="value: contrast, valueUpdate: 'input'"
              />
              <input type="text" size="5" data-bind="value: contrast" />
            </td>
          </tr>
          <tr>
            <td>Hue</td>
            <td>
              <input
                type="range"
                min="0"
                max="3"
                step="0.02"
                data-bind="value: hue, valueUpdate: 'input'"
              />
              <input type="text" size="5" data-bind="value: hue" />
            </td>
          </tr>
          <tr>
            <td>Saturation</td>
            <td>
              <input
                type="range"
                min="0"
                max="3"
                step="0.02"
                data-bind="value: saturation, valueUpdate: 'input'"
              />
              <input type="text" size="5" data-bind="value: saturation" />
            </td>
          </tr>
          <tr>
            <td>Gamma</td>
            <td>
              <input
                type="range"
                min="0"
                max="3"
                step="0.02"
                data-bind="value: gamma, valueUpdate: 'input'"
              />
              <input type="text" size="5" data-bind="value: gamma" />
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <script id="cesium_sandcastle_script">
      window.startup = async function (Cesium) {
        "use strict";
        //Sandcastle_Begin
        const viewer = new Cesium.Viewer("cesiumContainer");
        const imageryLayers = viewer.imageryLayers;

        // The viewModel tracks the state of our mini application.
        const viewModel = {
          brightness: 0,
          contrast: 0,
          hue: 0,
          saturation: 0,
          gamma: 0,
        };
        // Convert the viewModel members into knockout observables.
        Cesium.knockout.track(viewModel);

        // Bind the viewModel to the DOM elements of the UI that call for it.
        const toolbar = document.getElementById("toolbar");
        Cesium.knockout.applyBindings(viewModel, toolbar);

        // Make the active imagery layer a subscriber of the viewModel.
        function subscribeLayerParameter(name) {
          Cesium.knockout.getObservable(viewModel, name).subscribe(function (newValue) {
            if (imageryLayers.length > 0) {
              const layer = imageryLayers.get(0);
              layer[name] = newValue;
            }
          });
        }
        subscribeLayerParameter("brightness");
        subscribeLayerParameter("contrast");
        subscribeLayerParameter("hue");
        subscribeLayerParameter("saturation");
        subscribeLayerParameter("gamma");

        imageryLayers.get(0).saturation = 3.0;

        // Make the viewModel react to base layer changes.
        function updateViewModel() {
          if (imageryLayers.length > 0) {
            const layer = imageryLayers.get(0);
            viewModel.brightness = layer.brightness;
            viewModel.contrast = layer.contrast;
            viewModel.hue = layer.hue;
            viewModel.saturation = layer.saturation;
            viewModel.gamma = layer.gamma;
          }
        }
        imageryLayers.layerAdded.addEventListener(updateViewModel);
        imageryLayers.layerRemoved.addEventListener(updateViewModel);
        imageryLayers.layerMoved.addEventListener(updateViewModel);
        updateViewModel();
        //Sandcastle_End
      };
      if (typeof Cesium !== "undefined") {
        window.startupCalled = true;
        window.startup(Cesium).catch((error) => {
          "use strict";
          console.error(error);
        });
        Sandcastle.finishedLoading();
      }
    </script>
  </body>
</html>
